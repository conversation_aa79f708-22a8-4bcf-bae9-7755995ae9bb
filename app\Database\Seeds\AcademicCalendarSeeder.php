<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AcademicCalendarSeeder extends Seeder
{
    public function run()
    {
        // Get current session
        $sessionsModel = new \App\Models\SessionsModel();
        $currentSession = $sessionsModel->getCurrentSession();
        
        if (!$currentSession) {
            echo "No current session found. Please create a session first.\n";
            return;
        }

        // Get holiday types
        $holidayTypesModel = new \App\Models\HolidayTypesModel();
        $holidayTypes = $holidayTypesModel->findAll();
        
        if (empty($holidayTypes)) {
            echo "No holiday types found. Please run the migration first.\n";
            return;
        }

        // Create holiday type lookup
        $typeMap = [];
        foreach ($holidayTypes as $type) {
            $typeMap[$type['name']] = $type['id'];
        }

        // Extract academic year
        $academicYear = $currentSession['academic_year'] ?? '2024-2025';
        $startYear = (int)substr($academicYear, 0, 4);
        $endYear = $startYear + 1;

        // Sample holidays for the academic year
        $holidays = [
            // National Holidays
            [
                'title' => 'Independence Day',
                'description' => 'Pakistan Independence Day celebration',
                'start_date' => $startYear . '-08-14',
                'end_date' => $startYear . '-08-14',
                'holiday_type' => 'National Holiday',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],
            [
                'title' => 'Quaid-e-Azam Birthday',
                'description' => 'Founder of Pakistan birthday celebration',
                'start_date' => $startYear . '-12-25',
                'end_date' => $startYear . '-12-25',
                'holiday_type' => 'National Holiday',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],
            [
                'title' => 'Pakistan Day',
                'description' => 'Pakistan Resolution Day',
                'start_date' => $endYear . '-03-23',
                'end_date' => $endYear . '-03-23',
                'holiday_type' => 'National Holiday',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],

            // Religious Holidays
            [
                'title' => 'Eid ul-Fitr',
                'description' => 'Festival marking the end of Ramadan',
                'start_date' => $endYear . '-04-10',
                'end_date' => $endYear . '-04-12',
                'holiday_type' => 'Religious Holiday',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],
            [
                'title' => 'Eid ul-Adha',
                'description' => 'Festival of Sacrifice',
                'start_date' => $startYear . '-06-17',
                'end_date' => $startYear . '-06-19',
                'holiday_type' => 'Religious Holiday',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],
            [
                'title' => 'Ashura',
                'description' => 'Day of Ashura observance',
                'start_date' => $startYear . '-07-17',
                'end_date' => $startYear . '-07-17',
                'holiday_type' => 'Religious Holiday',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],

            // Academic Breaks
            [
                'title' => 'Summer Vacation',
                'description' => 'Annual summer break for students',
                'start_date' => $endYear . '-06-01',
                'end_date' => $endYear . '-06-30',
                'holiday_type' => 'Academic Break',
                'applies_to' => 'students',
                'is_recurring' => 1
            ],
            [
                'title' => 'Winter Break',
                'description' => 'Winter vacation and New Year break',
                'start_date' => $startYear . '-12-20',
                'end_date' => $endYear . '-01-05',
                'holiday_type' => 'Academic Break',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],
            [
                'title' => 'Mid-term Break',
                'description' => 'Semester break',
                'start_date' => $endYear . '-03-15',
                'end_date' => $endYear . '-03-22',
                'holiday_type' => 'Academic Break',
                'applies_to' => 'students',
                'is_recurring' => 1
            ],

            // Examination Periods
            [
                'title' => 'First Term Examinations',
                'description' => 'First semester final examinations',
                'start_date' => $startYear . '-11-15',
                'end_date' => $startYear . '-11-30',
                'holiday_type' => 'Examination Period',
                'applies_to' => 'students',
                'is_recurring' => 1
            ],
            [
                'title' => 'Second Term Examinations',
                'description' => 'Second semester final examinations',
                'start_date' => $endYear . '-05-01',
                'end_date' => $endYear . '-05-15',
                'holiday_type' => 'Examination Period',
                'applies_to' => 'students',
                'is_recurring' => 1
            ],

            // School Events
            [
                'title' => 'Annual Sports Day',
                'description' => 'School annual sports competition',
                'start_date' => $endYear . '-02-14',
                'end_date' => $endYear . '-02-14',
                'holiday_type' => 'School Event',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],
            [
                'title' => 'Science Fair',
                'description' => 'Annual science exhibition and competition',
                'start_date' => $endYear . '-01-20',
                'end_date' => $endYear . '-01-21',
                'holiday_type' => 'School Event',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],
            [
                'title' => 'Cultural Day',
                'description' => 'Celebration of cultural diversity',
                'start_date' => $startYear . '-09-15',
                'end_date' => $startYear . '-09-15',
                'holiday_type' => 'School Event',
                'applies_to' => 'all',
                'is_recurring' => 1
            ],

            // Staff Development
            [
                'title' => 'Teacher Training Workshop',
                'description' => 'Professional development for teaching staff',
                'start_date' => $startYear . '-08-01',
                'end_date' => $startYear . '-08-03',
                'holiday_type' => 'Staff Development',
                'applies_to' => 'students',
                'is_recurring' => 1
            ],
            [
                'title' => 'Curriculum Planning Day',
                'description' => 'Academic planning and curriculum review',
                'start_date' => $endYear . '-01-10',
                'end_date' => $endYear . '-01-10',
                'holiday_type' => 'Staff Development',
                'applies_to' => 'students',
                'is_recurring' => 1
            ]
        ];

        $createdCount = 0;
        $errors = [];

        foreach ($holidays as $holiday) {
            try {
                // Get holiday type ID
                $holidayTypeId = $typeMap[$holiday['holiday_type']] ?? null;
                
                if (!$holidayTypeId) {
                    $errors[] = "Holiday type '{$holiday['holiday_type']}' not found for holiday '{$holiday['title']}'";
                    continue;
                }

                // Prepare holiday data
                $holidayData = [
                    'session_id' => $currentSession['id'],
                    'holiday_type_id' => $holidayTypeId,
                    'title' => $holiday['title'],
                    'description' => $holiday['description'],
                    'start_date' => $holiday['start_date'],
                    'end_date' => $holiday['end_date'],
                    'is_recurring' => $holiday['is_recurring'],
                    'recurrence_pattern' => 'yearly',
                    'applies_to' => $holiday['applies_to'],
                    'notification_sent' => 0,
                    'is_active' => 'yes',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // Check if holiday already exists
                $existing = $this->db->table('academic_calendar')
                                    ->where('session_id', $currentSession['id'])
                                    ->where('title', $holiday['title'])
                                    ->get()
                                    ->getRowArray();

                if (!$existing) {
                    $this->db->table('academic_calendar')->insert($holidayData);
                    $createdCount++;
                }

            } catch (\Exception $e) {
                $errors[] = "Error creating holiday '{$holiday['title']}': " . $e->getMessage();
            }
        }

        echo "Academic Calendar Seeder completed!\n";
        echo "Session: {$currentSession['session']} ({$academicYear})\n";
        echo "Created: {$createdCount} holidays\n";
        
        if (!empty($errors)) {
            echo "Errors:\n";
            foreach ($errors as $error) {
                echo "  - {$error}\n";
            }
        }
    }
}
