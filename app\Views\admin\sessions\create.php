<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?? 'Create Academic Session' ?>
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Form -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Session Information</h3>
    </div>
    
    <div class="p-7">
        <form id="session-form">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Session Name -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">Session Name <span class="text-meta-1">*</span></label>
                    <input type="text" name="session" id="session" placeholder="e.g., 2024-1, 2024-2" 
                           class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                    <p class="text-sm text-gray-500 mt-1">Format: YYYY-S (Year-Semester)</p>
                </div>

                <!-- Academic Year -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">Academic Year <span class="text-meta-1">*</span></label>
                    <input type="text" name="academic_year" id="academic_year" placeholder="e.g., 2024-2025" 
                           class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                    <p class="text-sm text-gray-500 mt-1">Format: YYYY-YYYY</p>
                </div>

                <!-- Start Date -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">Start Date <span class="text-meta-1">*</span></label>
                    <input type="date" name="start_date" id="start_date" 
                           class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                </div>

                <!-- End Date -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">End Date <span class="text-meta-1">*</span></label>
                    <input type="date" name="end_date" id="end_date" 
                           class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary" required>
                </div>

                <!-- Status -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">Status</label>
                    <select name="is_active" id="is_active" 
                            class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                        <option value="yes">Active</option>
                        <option value="no">Inactive</option>
                    </select>
                </div>

                <!-- Set as Current -->
                <div>
                    <label class="mb-2.5 block text-black dark:text-white">Set as Current Session</label>
                    <select name="is_current" id="is_current" 
                            class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary">
                        <option value="no">No</option>
                        <option value="yes">Yes</option>
                    </select>
                    <p class="text-sm text-gray-500 mt-1">Only one session can be current at a time</p>
                </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label class="mb-2.5 block text-black dark:text-white">Description</label>
                <textarea name="description" id="description" rows="4" placeholder="Session description (optional)"
                          class="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"></textarea>
            </div>

            <!-- Submit Buttons -->
            <div class="mt-6 flex gap-4">
                <button type="submit" class="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-save mr-2"></i>
                    Create Session
                </button>
                <a href="<?= base_url($route_prefix) ?>" class="inline-flex items-center justify-center rounded-md border border-stroke px-6 py-3 text-center font-medium text-black hover:bg-gray dark:border-strokedark dark:text-white">
                    <i class="fas fa-times mr-2"></i>
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Auto-generate academic year based on session name
    $('#session').on('input', function() {
        const sessionName = $(this).val();
        const academicYear = generateAcademicYear(sessionName);
        if (academicYear) {
            $('#academic_year').val(academicYear);
        }
    });

    // Auto-set dates based on session pattern
    $('#session, #academic_year').on('input', function() {
        const sessionName = $('#session').val();
        const academicYear = $('#academic_year').val();
        const dates = generateSessionDates(sessionName, academicYear);
        
        if (dates.start_date) {
            $('#start_date').val(dates.start_date);
        }
        if (dates.end_date) {
            $('#end_date').val(dates.end_date);
        }
    });

    // Form submission
    $('#session-form').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        formData.append(csrf_token, csrf_hash);
        
        // Validate dates
        const startDate = new Date($('#start_date').val());
        const endDate = new Date($('#end_date').val());
        
        if (startDate >= endDate) {
            showToast('End date must be after start date', 'error');
            return;
        }
        
        $.ajax({
            url: '<?= base_url($route_prefix . '/store') ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false
        })
        .done(function(response) {
            if (response.success) {
                showToast(response.message, 'success');
                setTimeout(() => {
                    window.location.href = '<?= base_url($route_prefix) ?>';
                }, 1500);
            } else {
                showToast(response.message, 'error');
                if (response.errors) {
                    displayFormErrors(response.errors);
                }
            }
        })
        .fail(function() {
            showToast('An error occurred while creating the session', 'error');
        });
    });

    function generateAcademicYear(sessionName) {
        // Extract year from session name (e.g., "2024-1" -> "2024-2025")
        const match = sessionName.match(/(\d{4})/);
        if (match) {
            const year = parseInt(match[1]);
            return year + '-' + (year + 1);
        }
        return '';
    }

    function generateSessionDates(sessionName, academicYear) {
        const dates = { start_date: '', end_date: '' };
        
        // Extract year and semester from session name
        const match = sessionName.match(/(\d{4})-?(\d)/);
        if (match) {
            const year = parseInt(match[1]);
            const semester = parseInt(match[2]);
            
            if (semester === 1) {
                // First semester: July to December
                dates.start_date = year + '-07-01';
                dates.end_date = year + '-12-31';
            } else if (semester === 2) {
                // Second semester: January to June of next year
                dates.start_date = (year + 1) + '-01-01';
                dates.end_date = (year + 1) + '-06-30';
            }
        }
        
        return dates;
    }

    function displayFormErrors(errors) {
        // Clear previous errors
        $('.error-message').remove();
        
        // Display new errors
        for (const field in errors) {
            const errorMessage = errors[field];
            const fieldElement = $(`[name="${field}"]`);
            
            if (fieldElement.length) {
                fieldElement.addClass('border-red-500');
                fieldElement.after(`<p class="error-message text-red-500 text-sm mt-1">${errorMessage}</p>`);
            }
        }
    }

    // Clear error styling on input
    $('input, select, textarea').on('input change', function() {
        $(this).removeClass('border-red-500');
        $(this).next('.error-message').remove();
    });
});
</script>
<?= $this->endSection() ?>
