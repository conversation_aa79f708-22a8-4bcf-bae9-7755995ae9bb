<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddAttLogTable extends Migration
{
    public function up()
    {
        // Create att_log table for fingerprint device data
        $this->forge->addField([
            'att_id' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'default' => '0',
                'comment' => 'Attendance ID from device'
            ],
            'pin' => [
                'type' => 'VARCHAR',
                'constraint' => 32,
                'null' => false,
                'comment' => 'Employee PIN/ID'
            ],
            'scan_date' => [
                'type' => 'DATETIME',
                'null' => false
            ],
            'verifymode' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'comment' => '1=Fingerprint, 3=RFID Card, 20=Face Recognition'
            ],
            'status' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'default' => null,
                'comment' => '0: Absent, 1: Present, 2: Late, 3: Permission'
            ],
            'serialnumber' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'default' => null
            ],
            'student_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'default' => null
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'default' => null
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'default' => null
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'default' => null
            ],
            'sn' => [
                'type' => 'VARCHAR',
                'constraint' => 30,
                'null' => false,
                'default' => '',
                'comment' => 'Serial number of device'
            ],
            'inoutmode' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 0,
                'comment' => '0=Check In, 1=Check In, 2=Check Out, 3=Break Out, 4=Break In'
            ],
            'reserved' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 0,
                'comment' => 'Reserved field for future use'
            ],
            'work_code' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'default' => 0,
                'comment' => 'Work code for different work types'
            ]
        ]);

        $this->forge->addPrimaryKey('att_id');
        $this->forge->addKey('student_id');
        $this->forge->addKey('pin');
        $this->forge->addKey('sn');
        $this->forge->addKey('scan_date');
        $this->forge->addKey('verifymode');
        $this->forge->addKey('inoutmode');
        
        $this->forge->addForeignKey('student_id', 'students', 'id', 'SET NULL', 'SET NULL');
        
        $this->forge->createTable('att_log');

        // Add PIN field to students table
        $fields = [
            'biometric_pin' => [
                'type' => 'VARCHAR',
                'constraint' => 32,
                'null' => true,
                'default' => null,
                'comment' => 'PIN for biometric device',
                'after' => 'app_key'
            ]
        ];
        
        $this->forge->addColumn('students', $fields);
        $this->forge->addKey('students', 'biometric_pin');
    }

    public function down()
    {
        $this->forge->dropTable('att_log');
        $this->forge->dropColumn('students', 'biometric_pin');
    }
}
