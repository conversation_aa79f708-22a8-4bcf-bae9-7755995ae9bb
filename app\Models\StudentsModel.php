<?php

namespace App\Models;

class StudentsModel extends BaseModel
{
    protected $table = 'students';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'parent_id', 'admission_no', 'roll_no', 'admission_date', 'firstname', 'middlename', 
        'lastname', 'rte', 'image', 'mobileno', 'email', 'state', 'city', 'pincode', 
        'religion', 'cast', 'dob', 'gender', 'current_address', 'permanent_address', 
        'category_id', 'school_house_id', 'blood_group', 'hostel_room_id', 'adhar_no', 
        'samagra_id', 'bank_account_no', 'bank_name', 'ifsc_code', 'guardian_is', 
        'father_name', 'father_phone', 'father_occupation', 'mother_name', 'mother_phone', 
        'mother_occupation', 'guardian_name', 'guardian_relation', 'guardian_phone', 
        'guardian_occupation', 'guardian_address', 'guardian_email', 'father_pic',
        'mother_pic', 'guardian_pic', 'is_enroll', 'previous_school', 'height', 'weight',
        'note', 'measurement_date', 'dis_reason', 'dis_note', 'app_key', 'parent_app_key',
        'biometric_pin', 'disable_at', 'is_active'
    ];

    protected $validationRules = [
        'firstname' => 'required|min_length[2]|max_length[100]',
        'lastname' => 'required|min_length[2]|max_length[100]',
        'admission_no' => 'permit_empty|is_unique[students.admission_no,id,{id}]',
        'roll_no' => 'permit_empty|is_unique[students.roll_no,id,{id}]',
        'email' => 'permit_empty|valid_email|is_unique[students.email,id,{id}]',
        'mobileno' => 'permit_empty|numeric|min_length[10]|max_length[15]',
        'dob' => 'permit_empty|valid_date',
        'gender' => 'permit_empty|in_list[Male,Female,Other]',
        'blood_group' => 'permit_empty|in_list[A+,A-,B+,B-,AB+,AB-,O+,O-]',
        'admission_date' => 'permit_empty|valid_date'
    ];

    protected $validationMessages = [
        'firstname' => [
            'required' => 'First name is required',
            'min_length' => 'First name must be at least 2 characters long',
            'max_length' => 'First name cannot exceed 100 characters'
        ],
        'lastname' => [
            'required' => 'Last name is required',
            'min_length' => 'Last name must be at least 2 characters long',
            'max_length' => 'Last name cannot exceed 100 characters'
        ],
        'admission_no' => [
            'is_unique' => 'Admission number already exists'
        ],
        'roll_no' => [
            'is_unique' => 'Roll number already exists'
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'Email address already exists'
        ],
        'mobileno' => [
            'numeric' => 'Mobile number must contain only numbers',
            'min_length' => 'Mobile number must be at least 10 digits',
            'max_length' => 'Mobile number cannot exceed 15 digits'
        ]
    ];

    protected $searchableColumns = [
        'firstname', 'lastname', 'admission_no', 'roll_no', 'email', 'mobileno'
    ];

    protected $orderableColumns = [
        'id', 'admission_no', 'firstname', 'lastname', 'email', 'mobileno', 'admission_date', 'created_at'
    ];

    /**
     * Get students with their current session details
     */
    public function getStudentsWithSession($sessionId = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('students.*, student_session.class_id, student_session.section_id, 
                         classes.class, sections.section, sessions.session')
                ->join('student_session', 'students.id = student_session.student_id', 'left')
                ->join('classes', 'student_session.class_id = classes.id', 'left')
                ->join('sections', 'student_session.section_id = sections.id', 'left')
                ->join('sessions', 'student_session.session_id = sessions.id', 'left');

        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }

        $builder->orderBy('students.firstname', 'ASC');
        
        return $builder->get()->getResultArray();
    }

    /**
     * Get student by admission number
     */
    public function getByAdmissionNo($admissionNo)
    {
        return $this->where('admission_no', $admissionNo)->first();
    }

    /**
     * Get students by class and section
     */
    public function getByClassSection($classId, $sectionId, $sessionId = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('students.*, student_session.class_id, student_session.section_id')
                ->join('student_session', 'students.id = student_session.student_id')
                ->where('student_session.class_id', $classId)
                ->where('student_session.section_id', $sectionId);

        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }

        return $builder->get()->getResultArray();
    }

    /**
     * Generate next admission number
     */
    public function generateAdmissionNo($prefix = 'STU')
    {
        $year = date('Y');
        $lastStudent = $this->select('admission_no')
                           ->like('admission_no', $prefix . $year, 'after')
                           ->orderBy('id', 'DESC')
                           ->first();

        if ($lastStudent && $lastStudent['admission_no']) {
            $lastNumber = (int) substr($lastStudent['admission_no'], -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get student statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total students
        $stats['total'] = $this->countAllResults();
        
        // Active students
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        // Students by gender
        $stats['by_gender'] = $this->select('gender, COUNT(*) as count')
                                  ->groupBy('gender')
                                  ->findAll();
        
        // Students by class
        $stats['by_class'] = $this->db->table($this->table)
                                     ->select('classes.class, COUNT(students.id) as count')
                                     ->join('student_session', 'students.id = student_session.student_id')
                                     ->join('classes', 'student_session.class_id = classes.id')
                                     ->groupBy('classes.id')
                                     ->get()
                                     ->getResultArray();
        
        // Recent admissions (last 30 days)
        $stats['recent_admissions'] = $this->where('admission_date >=', date('Y-m-d', strtotime('-30 days')))
                                          ->countAllResults();
        
        return $stats;
    }

    /**
     * Search students
     */
    public function searchStudents($searchTerm, $filters = [])
    {
        $builder = $this->builder();
        
        if (!empty($searchTerm)) {
            $builder->groupStart()
                    ->like('firstname', $searchTerm)
                    ->orLike('lastname', $searchTerm)
                    ->orLike('admission_no', $searchTerm)
                    ->orLike('roll_no', $searchTerm)
                    ->orLike('email', $searchTerm)
                    ->orLike('mobileno', $searchTerm)
                    ->groupEnd();
        }
        
        // Apply additional filters
        if (!empty($filters['class_id'])) {
            $builder->join('student_session', 'students.id = student_session.student_id')
                    ->where('student_session.class_id', $filters['class_id']);
        }
        
        if (!empty($filters['section_id'])) {
            if (!strpos($builder->getCompiledSelect(), 'student_session')) {
                $builder->join('student_session', 'students.id = student_session.student_id');
            }
            $builder->where('student_session.section_id', $filters['section_id']);
        }
        
        if (!empty($filters['gender'])) {
            $builder->where('gender', $filters['gender']);
        }
        
        if (!empty($filters['is_active'])) {
            $builder->where('is_active', $filters['is_active']);
        }
        
        return $builder->orderBy('firstname', 'ASC')->findAll();
    }

    /**
     * Get students for dropdown
     */
    public function getForDropdown($sessionId = null)
    {
        $builder = $this->select('students.id, CONCAT(students.firstname, " ", students.lastname) as name, students.admission_no')
                        ->where('students.is_active', 'yes');
        
        if ($sessionId) {
            $builder->join('student_session', 'students.id = student_session.student_id')
                    ->where('student_session.session_id', $sessionId);
        }
        
        $students = $builder->orderBy('students.firstname', 'ASC')->findAll();
        
        $dropdown = [];
        foreach ($students as $student) {
            $dropdown[$student['id']] = $student['name'] . ' (' . $student['admission_no'] . ')';
        }
        
        return $dropdown;
    }

    /**
     * Get searchable columns for DataTables
     */
    protected function getSearchableColumns()
    {
        return $this->searchableColumns;
    }

    /**
     * Get orderable columns for DataTables
     */
    protected function getOrderableColumns()
    {
        return $this->orderableColumns;
    }

    /**
     * Load relationship data
     */
    protected function loadRelation($record, $relation)
    {
        switch ($relation) {
            case 'session':
                $record['session_data'] = $this->db->table('student_session')
                    ->select('student_session.*, classes.class, sections.section, sessions.session')
                    ->join('classes', 'student_session.class_id = classes.id', 'left')
                    ->join('sections', 'student_session.section_id = sections.id', 'left')
                    ->join('sessions', 'student_session.session_id = sessions.id', 'left')
                    ->where('student_session.student_id', $record['id'])
                    ->get()
                    ->getResultArray();
                break;
                
            case 'fees':
                $record['fees_data'] = $this->db->table('student_fees_deposite')
                    ->where('student_session_id IN (SELECT id FROM student_session WHERE student_id = ?)', $record['id'])
                    ->get()
                    ->getResultArray();
                break;
        }
        
        return $record;
    }
}
