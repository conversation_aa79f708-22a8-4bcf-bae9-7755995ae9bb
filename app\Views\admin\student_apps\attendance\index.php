<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Session Context Filter -->
<?= $this->include('admin/components/session_filter') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?? 'Student Attendance Management' ?>
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-6">
    <!-- Today's Attendance -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-calendar-day text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="today-attendance">0</h4>
                <span class="text-sm font-medium">Today's Attendance</span>
            </div>
        </div>
    </div>

    <!-- Present Today -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-user-check text-success text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="present-today">0</h4>
                <span class="text-sm font-medium">Present Today</span>
            </div>
        </div>
    </div>

    <!-- Absent Today -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-user-times text-danger text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="absent-today">0</h4>
                <span class="text-sm font-medium">Absent Today</span>
            </div>
        </div>
    </div>

    <!-- Attendance Percentage -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-percentage text-warning text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="attendance-percentage">0%</h4>
                <span class="text-sm font-medium">Attendance Rate</span>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-black dark:text-white">Filters & Actions</h3>
            <div class="flex gap-2">
                <a href="<?= base_url($route_prefix . '/create') ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-plus mr-2"></i>
                    Mark Attendance
                </a>
                <button onclick="syncBiometric()" class="inline-flex items-center justify-center rounded-md bg-success px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-sync mr-2"></i>
                    Sync Biometric
                </button>
                <button onclick="showDeviceStats()" class="inline-flex items-center justify-center rounded-md bg-info px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-chart-bar mr-2"></i>
                    Device Stats
                </button>
                <a href="<?= base_url('admin/academic-calendar') ?>" class="inline-flex items-center justify-center rounded-md bg-warning px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Calendar
                </a>
                <button id="bulk-attendance" class="inline-flex items-center justify-center rounded-md bg-success px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-users mr-2"></i>
                    Bulk Attendance
                </button>
            </div>
        </div>
    </div>
    
    <div class="p-7">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Class</label>
                <select id="filter-class" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Classes</option>
                    <?php foreach ($classes as $id => $name): ?>
                        <option value="<?= $id ?>"><?= esc($name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Section</label>
                <select id="filter-section" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Sections</option>
                    <?php foreach ($sections as $id => $name): ?>
                        <option value="<?= $id ?>"><?= esc($name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Date</label>
                <input type="date" id="filter-date" value="<?= date('Y-m-d') ?>" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
            </div>
            
            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Attendance Type</label>
                <select id="filter-attendance-type" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Types</option>
                    <option value="Present">Present</option>
                    <option value="Absent">Absent</option>
                    <option value="Late">Late</option>
                    <option value="Half Day">Half Day</option>
                </select>
            </div>

            <div>
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Source</label>
                <select id="filter-source" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="">All Sources</option>
                    <option value="manual">Manual Entry</option>
                    <option value="biometric">Biometric Device</option>
                </select>
            </div>
        </div>
        
        <div class="mt-4 flex gap-2">
            <button id="apply-filters" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                <i class="fas fa-filter mr-2"></i>
                Apply Filters
            </button>
            <button id="clear-filters" class="inline-flex items-center justify-center rounded-md border border-stroke px-4 py-2 text-center font-medium text-black hover:bg-gray dark:border-strokedark dark:text-white">
                <i class="fas fa-times mr-2"></i>
                Clear
            </button>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Student Attendance Records</h3>
    </div>
    
    <div class="p-7">
        <div class="overflow-x-auto">
            <table id="attendance-table" class="w-full table-auto">
                <thead>
                    <tr class="bg-gray-2 text-left dark:bg-meta-4">
                        <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">#</th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Student</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Admission No</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Class</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Session</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Academic Year</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Date</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Attendance</th>
                        <th class="min-w-[80px] py-4 px-4 font-medium text-black dark:text-white">Source</th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Remark</th>
                        <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via DataTables AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Load statistics
    loadStats();
    
    // Initialize DataTable
    const table = $('#attendance-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url($route_prefix . '/get-data') ?>',
            type: 'POST',
            data: function(d) {
                d[csrf_token] = csrf_hash;
                d.class_id = $('#filter-class').val();
                d.section_id = $('#filter-section').val();
                d.date = $('#filter-date').val();
                d.attendance_type = $('#filter-attendance-type').val();
                d.source = $('#filter-source').val();
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'student_name', name: 'student_name' },
            { data: 'admission_no', name: 'admission_no' },
            { data: 'class_section', name: 'class_section' },
            { data: 'session', name: 'session' },
            { data: 'academic_year', name: 'academic_year' },
            { data: 'date', name: 'date' },
            { data: 'attendance_type', name: 'attendance_type', orderable: false },
            { data: 'source', name: 'source', orderable: false },
            { data: 'remark', name: 'remark' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        responsive: true
    });

    // Apply filters
    $('#apply-filters').click(function() {
        table.ajax.reload();
        loadStats();
    });

    // Clear filters
    $('#clear-filters').click(function() {
        $('#filter-class').val('');
        $('#filter-section').val('');
        $('#filter-date').val('<?= date('Y-m-d') ?>');
        $('#filter-attendance-type').val('');
        $('#filter-source').val('');
        table.ajax.reload();
        loadStats();
    });

    // Delete function
    window.deleteRecord = function(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . '/delete') ?>/' + id,
                    type: 'DELETE',
                    data: {
                        [csrf_token]: csrf_hash
                    }
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while deleting the attendance record', 'error');
                });
            }
        });
    };

    function loadStats() {
        $.get('<?= base_url($route_prefix . '/get-stats') ?>')
            .done(function(response) {
                if (response.success) {
                    $('#today-attendance').text(response.data.today);
                    $('#present-today').text(response.data.present_today);
                    $('#absent-today').text(response.data.absent_today);
                    $('#attendance-percentage').text(response.data.attendance_percentage + '%');
                }
            })
            .fail(function() {
                console.log('Failed to load statistics');
            });
    }

    // Biometric sync function
    window.syncBiometric = function() {
        Swal.fire({
            title: 'Sync Biometric Data',
            html: '<input id="sync-date" type="date" class="swal2-input" value="<?= date('Y-m-d') ?>">',
            showCancelButton: true,
            confirmButtonText: 'Validate & Sync',
            preConfirm: () => {
                const date = document.getElementById('sync-date').value;
                if (!date) {
                    Swal.showValidationMessage('Please select a date');
                    return false;
                }
                return date;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // First validate the date
                validateAttendanceDate(result.value, function(validation) {
                    if (!validation.is_valid) {
                        Swal.fire({
                            title: 'Cannot Sync Attendance',
                            text: validation.message,
                            icon: 'error'
                        });
                        return;
                    }

                    let confirmMessage = `Sync biometric data for ${result.value}?`;
                    if (validation.is_holiday) {
                        confirmMessage += `\n\nNote: This is a holiday (${validation.holiday_info.title})`;
                        if (!validation.affects_attendance) {
                            confirmMessage += ' but attendance can still be marked.';
                        }
                    }

                    Swal.fire({
                        title: 'Confirm Sync',
                        text: confirmMessage,
                        icon: validation.is_holiday ? 'warning' : 'question',
                        showCancelButton: true,
                        confirmButtonText: 'Sync'
                    }).then((confirmResult) => {
                        if (confirmResult.isConfirmed) {
                            performBiometricSync(result.value);
                        }
                    });
                });
            }
        });
    };

    function validateAttendanceDate(date, callback) {
        $.ajax({
            url: '<?= base_url($route_prefix . '/validate-date') ?>',
            type: 'POST',
            data: {
                date: date,
                [csrf_token]: csrf_hash
            }
        })
        .done(function(response) {
            if (response.success) {
                callback(response.data);
            } else {
                showToast('Failed to validate date', 'error');
            }
        })
        .fail(function() {
            showToast('Failed to validate date', 'error');
        });
    }

    function performBiometricSync(date) {
        $.ajax({
            url: '<?= base_url($route_prefix . '/sync-biometric') ?>',
            type: 'POST',
            data: {
                date: date,
                [csrf_token]: csrf_hash
            }
        })
        .done(function(response) {
            if (response.success) {
                showToast(response.message, 'success');
                table.ajax.reload();
                loadStats();
            } else {
                showToast(response.message, 'error');
            }
        })
        .fail(function() {
            showToast('Failed to sync biometric data', 'error');
        });
    }

    // Show device statistics
    window.showDeviceStats = function() {
        $.get('<?= base_url($route_prefix . '/device-stats') ?>')
            .done(function(response) {
                if (response.success) {
                    let html = '<div class="text-left">';
                    if (response.data.length === 0) {
                        html += '<p>No device data found.</p>';
                    } else {
                        response.data.forEach(function(device) {
                            html += '<div class="mb-4 p-3 border rounded">';
                            html += '<h4 class="font-bold">Device: ' + device.device_serial + '</h4>';
                            html += '<p>Total Records: ' + device.total_records + '</p>';
                            html += '<p>First Scan: ' + device.first_scan + '</p>';
                            html += '<p>Last Scan: ' + device.last_scan + '</p>';
                            html += '</div>';
                        });
                    }
                    html += '</div>';

                    Swal.fire({
                        title: 'Device Statistics',
                        html: html,
                        width: '600px'
                    });
                }
            })
            .fail(function() {
                showToast('Failed to load device statistics', 'error');
            });
    };
});
</script>
<?= $this->endSection() ?>
