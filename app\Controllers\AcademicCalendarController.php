<?php

namespace App\Controllers;

use App\Models\AcademicCalendarModel;
use App\Models\HolidayTypesModel;
use App\Models\SessionsModel;
use App\Services\CalendarService;

class AcademicCalendarController extends BaseCrudController
{
    protected $calendarService;
    protected $holidayTypesModel;
    protected $sessionsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new AcademicCalendarModel();
        $this->holidayTypesModel = new HolidayTypesModel();
        $this->sessionsModel = new SessionsModel();
        $this->calendarService = CalendarService::getInstance();
        
        $this->viewPath = 'admin/academic_calendar';
        $this->routePrefix = 'admin/academic-calendar';
        $this->entityName = 'Academic Calendar';
        $this->entityNamePlural = 'Academic Calendar';
    }

    /**
     * Display calendar view
     */
    public function index()
    {
        helper('session');
        
        $data = [
            'title' => 'Academic Calendar',
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'holiday_types' => $this->holidayTypesModel->getForDropdown(),
            'sessions' => $this->sessionsModel->getForDropdown(),
            'current_session' => get_current_session(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Academic Calendar', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get calendar events for FullCalendar
     */
    public function getEvents()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $start = $this->request->getGet('start');
        $end = $this->request->getGet('end');
        $sessionId = $this->request->getGet('session_id') ?: session()->get('current_session_id');

        $events = $this->model->getCalendarEvents([
            'session_id' => $sessionId,
            'start_date' => $start,
            'end_date' => $end
        ]);

        $calendarEvents = [];
        foreach ($events as $event) {
            $calendarEvents[] = [
                'id' => $event['id'],
                'title' => $event['title'],
                'start' => $event['start_date'],
                'end' => date('Y-m-d', strtotime($event['end_date'] . ' +1 day')),
                'color' => $event['color'],
                'description' => $event['description'],
                'extendedProps' => [
                    'holiday_type' => $event['holiday_type_name'],
                    'affects_attendance' => $event['affects_attendance'],
                    'is_academic_break' => $event['is_academic_break'],
                    'applies_to' => $event['applies_to'],
                    'session_name' => $event['session_name']
                ]
            ];
        }

        return $this->response->setJSON($calendarEvents);
    }

    /**
     * Create new holiday
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        
        // Add current session if not provided
        if (empty($data['session_id'])) {
            helper('session');
            $data['session_id'] = get_current_session_id();
        }

        // Add created_by
        $data['created_by'] = user_id();

        $result = $this->model->createHoliday($data);
        return $this->response->setJSON($result);
    }

    /**
     * Update holiday
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        
        // Validate date range
        if (strtotime($data['start_date']) > strtotime($data['end_date'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'End date must be after or equal to start date'
            ]);
        }

        $result = $this->model->update($id, $data);

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Holiday updated successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to update holiday',
            'errors' => $this->model->errors()
        ]);
    }

    /**
     * Get holiday details
     */
    public function show($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $holiday = $this->model->getCalendarEvents(['id' => $id]);
        
        if (empty($holiday)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Holiday not found'
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'data' => $holiday[0]
        ]);
    }

    /**
     * Get calendar statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $sessionId = $this->request->getGet('session_id') ?: session()->get('current_session_id');
        $summary = $this->calendarService->getCalendarSummary($sessionId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $summary
        ]);
    }

    /**
     * Validate attendance date
     */
    public function validateDate()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $date = $this->request->getPost('date');
        $sessionId = $this->request->getPost('session_id') ?: session()->get('current_session_id');

        $validation = $this->calendarService->validateAttendanceDate($date, $sessionId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $validation
        ]);
    }

    /**
     * Get upcoming holidays
     */
    public function getUpcoming()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $limit = $this->request->getGet('limit') ?: 5;
        $sessionId = $this->request->getGet('session_id') ?: session()->get('current_session_id');

        $holidays = $this->calendarService->getUpcomingHolidays($limit, $sessionId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $holidays
        ]);
    }

    /**
     * Generate default calendar
     */
    public function generateDefault()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $sessionId = $this->request->getPost('session_id');
        $academicYear = $this->request->getPost('academic_year');

        if (!$sessionId || !$academicYear) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Session ID and Academic Year are required'
            ]);
        }

        $result = $this->calendarService->generateDefaultCalendar($sessionId, $academicYear);
        return $this->response->setJSON($result);
    }

    /**
     * Generate recurring holidays
     */
    public function generateRecurring()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $fromSessionId = $this->request->getPost('from_session_id');
        $toSessionId = $this->request->getPost('to_session_id');

        if (!$fromSessionId || !$toSessionId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Both source and target sessions are required'
            ]);
        }

        $result = $this->model->generateRecurringHolidays($fromSessionId, $toSessionId);
        return $this->response->setJSON($result);
    }

    /**
     * Holiday Types Management
     */
    public function holidayTypes()
    {
        $data = [
            'title' => 'Holiday Types Management',
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Academic Calendar', 'url' => base_url($this->routePrefix)],
                ['name' => 'Holiday Types', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/holiday_types', $data);
    }

    /**
     * Get holiday types data
     */
    public function getHolidayTypesData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $types = $this->holidayTypesModel->getWithUsageCount();

        $data = [];
        foreach ($types as $type) {
            $data[] = [
                'id' => $type['id'],
                'name' => $type['name'],
                'description' => $type['description'] ?: '-',
                'color' => '<span class="inline-block w-6 h-6 rounded" style="background-color: ' . $type['color'] . '"></span> ' . $type['color'],
                'is_academic_break' => $type['is_academic_break'] ? '<span class="text-success">Yes</span>' : '<span class="text-muted">No</span>',
                'affects_attendance' => $type['affects_attendance'] ? '<span class="text-warning">Yes</span>' : '<span class="text-muted">No</span>',
                'usage_count' => $type['usage_count'],
                'is_active' => $this->getStatusBadge($type['is_active']),
                'actions' => $this->getHolidayTypeActions($type)
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Store holiday type
     */
    public function storeHolidayType()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->holidayTypesModel->insert($data);

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Holiday type created successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to create holiday type',
            'errors' => $this->holidayTypesModel->errors()
        ]);
    }

    /**
     * Update holiday type
     */
    public function updateHolidayType($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->holidayTypesModel->update($id, $data);

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Holiday type updated successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to update holiday type',
            'errors' => $this->holidayTypesModel->errors()
        ]);
    }

    /**
     * Delete holiday type
     */
    public function deleteHolidayType($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        if (!$this->holidayTypesModel->canDelete($id)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Cannot delete holiday type that is being used in calendar'
            ]);
        }

        $result = $this->holidayTypesModel->delete($id);

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Holiday type deleted successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to delete holiday type'
        ]);
    }

    /**
     * Get status badge HTML
     */
    private function getStatusBadge($status)
    {
        if ($status === 'yes') {
            return '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Active</span>';
        } else {
            return '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Inactive</span>';
        }
    }

    /**
     * Get holiday type action buttons
     */
    private function getHolidayTypeActions($type)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<button onclick="editHolidayType(' . $type['id'] . ')" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>';

        if ($type['usage_count'] == 0) {
            $buttons .= '<button onclick="deleteHolidayType(' . $type['id'] . ')" class="hover:text-danger" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>';
        }

        $buttons .= '</div>';

        return $buttons;
    }
}
