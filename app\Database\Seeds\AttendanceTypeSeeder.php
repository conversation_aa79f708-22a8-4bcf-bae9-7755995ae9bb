<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class AttendanceTypeSeeder extends Seeder
{
    public function run()
    {
        $attendanceTypes = [
            [
                'id' => 1,
                'type' => 'Present',
                'key_value' => 'present',
                'long_lang_name' => 'Present',
                'long_name_style' => 'success',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'type' => 'Absent',
                'key_value' => 'absent',
                'long_lang_name' => 'Absent',
                'long_name_style' => 'danger',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 3,
                'type' => 'Late',
                'key_value' => 'late',
                'long_lang_name' => 'Late',
                'long_name_style' => 'warning',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 4,
                'type' => 'Half Day',
                'key_value' => 'half_day',
                'long_lang_name' => 'Half Day',
                'long_name_style' => 'info',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 5,
                'type' => 'Permission',
                'key_value' => 'permission',
                'long_lang_name' => 'Permission',
                'long_name_style' => 'primary',
                'is_active' => 'yes',
                'for_qr_attendance' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insert new data (ignore duplicates)
        foreach ($attendanceTypes as $type) {
            $existing = $this->db->table('attendence_type')->where('id', $type['id'])->get()->getRowArray();
            if (!$existing) {
                $this->db->table('attendence_type')->insert($type);
            }
        }

        echo "Attendance types seeded successfully!\n";
        echo "Created " . count($attendanceTypes) . " attendance types\n";
    }
}
