<?php

namespace App\Models;

use CodeIgniter\Model;

class AttLogModel extends Model
{
    protected $table = 'att_log';
    protected $primaryKey = 'att_id';
    protected $allowedFields = [
        'att_id', 'pin', 'scan_date', 'verifymode', 'status', 'serialnumber',
        'student_id', 'sn', 'inoutmode', 'reserved', 'work_code'
    ];

    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $validationRules = [
        'att_id' => 'required|max_length[50]',
        'pin' => 'required|max_length[32]',
        'scan_date' => 'required|valid_date',
        'verifymode' => 'required|integer',
        'inoutmode' => 'required|integer'
    ];

    protected $validationMessages = [
        'att_id' => [
            'required' => 'Attendance ID is required',
            'max_length' => 'Attendance ID cannot exceed 50 characters'
        ],
        'pin' => [
            'required' => 'PIN is required',
            'max_length' => 'PIN cannot exceed 32 characters'
        ],
        'scan_date' => [
            'required' => 'Scan date is required',
            'valid_date' => 'Please enter a valid scan date'
        ]
    ];

    /**
     * Get attendance logs with student details
     */
    public function getAttendanceWithStudents($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('att_log.*, students.firstname, students.lastname, students.admission_no, 
                         students.roll_no, classes.class, sections.section')
                ->join('students', 'att_log.student_id = students.id', 'left')
                ->join('student_session', 'students.id = student_session.student_id', 'left')
                ->join('classes', 'student_session.class_id = classes.id', 'left')
                ->join('sections', 'student_session.section_id = sections.id', 'left');

        // Apply filters
        if (!empty($filters['date_from'])) {
            $builder->where('DATE(att_log.scan_date) >=', $filters['date_from']);
        }
        
        if (!empty($filters['date_to'])) {
            $builder->where('DATE(att_log.scan_date) <=', $filters['date_to']);
        }
        
        if (!empty($filters['student_id'])) {
            $builder->where('att_log.student_id', $filters['student_id']);
        }
        
        if (!empty($filters['device_sn'])) {
            $builder->where('att_log.sn', $filters['device_sn']);
        }
        
        if (!empty($filters['inoutmode'])) {
            $builder->where('att_log.inoutmode', $filters['inoutmode']);
        }

        return $builder->get()->getResultArray();
    }

    /**
     * Get daily attendance summary
     */
    public function getDailyAttendanceSummary($date)
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_id, MIN(scan_date) as first_scan, MAX(scan_date) as last_scan,
                         COUNT(*) as total_scans')
                ->where('DATE(scan_date)', $date)
                ->where('student_id IS NOT NULL')
                ->groupBy('student_id');

        return $builder->get()->getResultArray();
    }

    /**
     * Link PIN to student
     */
    public function linkPinToStudent($pin, $studentId)
    {
        // Update students table with biometric PIN
        $studentsModel = new StudentsModel();
        $result = $studentsModel->update($studentId, ['biometric_pin' => $pin]);
        
        if ($result) {
            // Update all att_log records with this PIN to link to student
            $this->where('pin', $pin)
                 ->where('student_id IS NULL')
                 ->set(['student_id' => $studentId])
                 ->update();
        }
        
        return $result;
    }

    /**
     * Get unlinked attendance records (no student_id)
     */
    public function getUnlinkedRecords($limit = 100)
    {
        return $this->where('student_id IS NULL')
                   ->orderBy('scan_date', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Sync attendance data from device
     */
    public function syncFromDevice($deviceData)
    {
        $syncedCount = 0;
        $errors = [];

        foreach ($deviceData as $record) {
            try {
                // Check if record already exists
                $existing = $this->where('att_id', $record['att_id'])->first();
                
                if (!$existing) {
                    // Try to find student by PIN
                    $studentsModel = new StudentsModel();
                    $student = $studentsModel->where('biometric_pin', $record['pin'])->first();
                    
                    $record['student_id'] = $student ? $student['id'] : null;
                    
                    if ($this->insert($record)) {
                        $syncedCount++;
                    }
                } else {
                    // Update existing record if needed
                    if ($this->update($record['att_id'], $record)) {
                        $syncedCount++;
                    }
                }
            } catch (\Exception $e) {
                $errors[] = "Error syncing record {$record['att_id']}: " . $e->getMessage();
            }
        }

        return [
            'synced_count' => $syncedCount,
            'errors' => $errors
        ];
    }

    /**
     * Get device statistics
     */
    public function getDeviceStats()
    {
        $builder = $this->db->table($this->table);
        $builder->select('sn as device_serial, COUNT(*) as total_records, 
                         MAX(scan_date) as last_scan, MIN(scan_date) as first_scan')
                ->groupBy('sn')
                ->orderBy('last_scan', 'DESC');

        return $builder->get()->getResultArray();
    }

    /**
     * Get attendance patterns for analysis
     */
    public function getAttendancePatterns($studentId, $dateFrom, $dateTo)
    {
        $builder = $this->db->table($this->table);
        $builder->select('DATE(scan_date) as date, 
                         MIN(CASE WHEN inoutmode IN (0,1) THEN scan_date END) as check_in,
                         MAX(CASE WHEN inoutmode = 2 THEN scan_date END) as check_out,
                         COUNT(*) as total_scans')
                ->where('student_id', $studentId)
                ->where('DATE(scan_date) >=', $dateFrom)
                ->where('DATE(scan_date) <=', $dateTo)
                ->groupBy('DATE(scan_date)')
                ->orderBy('date', 'ASC');

        return $builder->get()->getResultArray();
    }
}
