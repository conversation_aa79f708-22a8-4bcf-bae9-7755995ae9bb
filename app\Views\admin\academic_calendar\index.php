<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Session Context Filter -->
<?= $this->include('admin/components/session_filter') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?? 'Academic Calendar' ?>
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-6">
    <!-- Total Holidays -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-calendar-day text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="total-holidays" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Total Holidays</span>
            </div>
        </div>
    </div>

    <!-- Upcoming Holidays -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-clock text-success text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="upcoming-holidays" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Upcoming</span>
            </div>
        </div>
    </div>

    <!-- Academic Breaks -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-pause-circle text-warning text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="academic-breaks" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Academic Breaks</span>
            </div>
        </div>
    </div>

    <!-- Attendance Affecting -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-user-times text-danger text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="attendance-affecting" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Affect Attendance</span>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Actions -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-black dark:text-white">Calendar Management</h3>
            <div class="flex gap-2">
                <button onclick="addHoliday()" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-plus mr-2"></i>
                    Add Holiday
                </button>
                <button onclick="generateDefault()" class="inline-flex items-center justify-center rounded-md bg-success px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-magic mr-2"></i>
                    Generate Default
                </button>
                <a href="<?= base_url($route_prefix . '/holiday-types') ?>" class="inline-flex items-center justify-center rounded-md bg-info px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-tags mr-2"></i>
                    Holiday Types
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Calendar View -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Academic Calendar</h3>
    </div>
    
    <div class="p-7">
        <div id="academic-calendar"></div>
    </div>
</div>

<!-- Upcoming Holidays Panel -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mt-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Upcoming Holidays</h3>
    </div>
    
    <div class="p-7">
        <div id="upcoming-holidays-list">
            <!-- Upcoming holidays will be loaded here -->
        </div>
    </div>
</div>

<!-- Add/Edit Holiday Modal -->
<div id="holiday-modal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50">
    <div class="w-full max-w-md rounded-lg bg-white p-6 dark:bg-boxdark">
        <div class="mb-4 flex items-center justify-between">
            <h3 id="modal-title" class="text-lg font-semibold text-black dark:text-white">Add Holiday</h3>
            <button onclick="closeHolidayModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form id="holiday-form">
            <input type="hidden" id="holiday-id" name="id">
            
            <div class="mb-4">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Title <span class="text-red-500">*</span></label>
                <input type="text" id="holiday-title" name="title" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white" required>
            </div>
            
            <div class="mb-4">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Holiday Type <span class="text-red-500">*</span></label>
                <select id="holiday-type" name="holiday_type_id" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white" required>
                    <option value="">Select Holiday Type</option>
                    <?php foreach ($holiday_types as $id => $name): ?>
                        <option value="<?= $id ?>"><?= esc($name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="mb-2 block text-sm font-medium text-black dark:text-white">Start Date <span class="text-red-500">*</span></label>
                    <input type="date" id="holiday-start-date" name="start_date" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white" required>
                </div>
                
                <div>
                    <label class="mb-2 block text-sm font-medium text-black dark:text-white">End Date <span class="text-red-500">*</span></label>
                    <input type="date" id="holiday-end-date" name="end_date" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white" required>
                </div>
            </div>
            
            <div class="mb-4">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Description</label>
                <textarea id="holiday-description" name="description" rows="3" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white"></textarea>
            </div>
            
            <div class="mb-4">
                <label class="mb-2 block text-sm font-medium text-black dark:text-white">Applies To</label>
                <select id="holiday-applies-to" name="applies_to" class="w-full rounded border border-stroke bg-gray py-3 px-4 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                    <option value="all">All (Students & Staff)</option>
                    <option value="students">Students Only</option>
                    <option value="staff">Staff Only</option>
                </select>
            </div>
            
            <div class="flex gap-4">
                <button type="submit" class="flex-1 rounded bg-primary py-2 px-4 text-white hover:bg-opacity-90">
                    <i class="fas fa-save mr-2"></i>
                    Save Holiday
                </button>
                <button type="button" onclick="closeHolidayModal()" class="flex-1 rounded border border-stroke py-2 px-4 text-black hover:bg-gray dark:border-strokedark dark:text-white">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script>
$(document).ready(function() {
    // Load statistics
    loadStats();
    
    // Load upcoming holidays
    loadUpcomingHolidays();
    
    // Initialize FullCalendar
    initializeCalendar();
    
    // Holiday form submission
    $('#holiday-form').on('submit', function(e) {
        e.preventDefault();
        saveHoliday();
    });
});

let calendar;

function initializeCalendar() {
    const calendarEl = document.getElementById('academic-calendar');
    
    calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,listWeek'
        },
        events: {
            url: '<?= base_url($route_prefix . '/events') ?>',
            extraParams: function() {
                return {
                    session_id: $('#session-context-selector').val() || ''
                };
            }
        },
        eventClick: function(info) {
            showHolidayDetails(info.event);
        },
        dateClick: function(info) {
            addHoliday(info.dateStr);
        },
        eventDidMount: function(info) {
            // Add tooltip
            info.el.title = info.event.extendedProps.description || info.event.title;
        }
    });
    
    calendar.render();
}

function loadStats() {
    $.get('<?= base_url($route_prefix . '/stats') ?>')
        .done(function(response) {
            if (response.success) {
                const stats = response.data.statistics;
                $('#total-holidays').text(stats.total_holidays || 0);
                $('#upcoming-holidays').text(stats.upcoming_holidays || 0);
                $('#academic-breaks').text(stats.academic_breaks || 0);
                $('#attendance-affecting').text(stats.attendance_affecting || 0);
            }
        })
        .fail(function() {
            console.log('Failed to load statistics');
        });
}

function loadUpcomingHolidays() {
    $.get('<?= base_url($route_prefix . '/upcoming') ?>')
        .done(function(response) {
            if (response.success) {
                displayUpcomingHolidays(response.data);
            }
        })
        .fail(function() {
            console.log('Failed to load upcoming holidays');
        });
}

function displayUpcomingHolidays(holidays) {
    const container = $('#upcoming-holidays-list');
    
    if (holidays.length === 0) {
        container.html('<p class="text-gray-500">No upcoming holidays</p>');
        return;
    }
    
    let html = '';
    holidays.forEach(function(holiday) {
        const dateRange = formatDateRange(holiday.start_date, holiday.end_date);
        html += `
            <div class="mb-4 p-4 border rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-semibold text-black dark:text-white">${holiday.title}</h4>
                        <p class="text-sm text-gray-600">${dateRange}</p>
                        <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: ${holiday.color}"></span>
                        <span class="text-sm">${holiday.holiday_type_name}</span>
                    </div>
                    <div class="text-right">
                        ${holiday.affects_attendance ? '<span class="text-red-500 text-sm">Affects Attendance</span>' : ''}
                    </div>
                </div>
            </div>
        `;
    });
    
    container.html(html);
}

function addHoliday(date = null) {
    $('#modal-title').text('Add Holiday');
    $('#holiday-form')[0].reset();
    $('#holiday-id').val('');
    
    if (date) {
        $('#holiday-start-date').val(date);
        $('#holiday-end-date').val(date);
    }
    
    $('#holiday-modal').removeClass('hidden').addClass('flex');
}

function closeHolidayModal() {
    $('#holiday-modal').removeClass('flex').addClass('hidden');
}

function saveHoliday() {
    const formData = new FormData($('#holiday-form')[0]);
    formData.append(csrf_token, csrf_hash);
    
    const holidayId = $('#holiday-id').val();
    const url = holidayId ? 
        '<?= base_url($route_prefix . '/update') ?>/' + holidayId : 
        '<?= base_url($route_prefix . '/store') ?>';
    
    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false
    })
    .done(function(response) {
        if (response.success) {
            showToast(response.message, 'success');
            closeHolidayModal();
            calendar.refetchEvents();
            loadStats();
            loadUpcomingHolidays();
        } else {
            showToast(response.message, 'error');
        }
    })
    .fail(function() {
        showToast('Failed to save holiday', 'error');
    });
}

function generateDefault() {
    const currentSession = <?= json_encode($current_session) ?>;
    
    if (!currentSession) {
        showToast('No active session found', 'error');
        return;
    }
    
    Swal.fire({
        title: 'Generate Default Calendar',
        text: `Generate default holidays for ${currentSession.session} (${currentSession.academic_year})?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Generate'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?= base_url($route_prefix . '/generate-default') ?>',
                type: 'POST',
                data: {
                    session_id: currentSession.id,
                    academic_year: currentSession.academic_year,
                    [csrf_token]: csrf_hash
                }
            })
            .done(function(response) {
                if (response.success) {
                    showToast(response.message, 'success');
                    calendar.refetchEvents();
                    loadStats();
                    loadUpcomingHolidays();
                } else {
                    showToast(response.message, 'error');
                }
            })
            .fail(function() {
                showToast('Failed to generate default calendar', 'error');
            });
        }
    });
}

function formatDateRange(startDate, endDate) {
    if (startDate === endDate) {
        return new Date(startDate).toLocaleDateString();
    }
    return new Date(startDate).toLocaleDateString() + ' - ' + new Date(endDate).toLocaleDateString();
}

function showHolidayDetails(event) {
    const props = event.extendedProps;
    
    Swal.fire({
        title: event.title,
        html: `
            <div class="text-left">
                <p><strong>Type:</strong> ${props.holiday_type}</p>
                <p><strong>Date:</strong> ${formatDateRange(event.startStr, event.endStr)}</p>
                <p><strong>Applies To:</strong> ${props.applies_to}</p>
                <p><strong>Academic Break:</strong> ${props.is_academic_break ? 'Yes' : 'No'}</p>
                <p><strong>Affects Attendance:</strong> ${props.affects_attendance ? 'Yes' : 'No'}</p>
                ${event.extendedProps.description ? `<p><strong>Description:</strong> ${event.extendedProps.description}</p>` : ''}
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Edit',
        cancelButtonText: 'Close'
    }).then((result) => {
        if (result.isConfirmed) {
            editHoliday(event.id);
        }
    });
}

function editHoliday(eventId) {
    // Implementation for editing holiday
    // This would load the holiday data and populate the form
}

// Refresh calendar when session context changes
$(document).on('change', '#session-context-selector', function() {
    if (calendar) {
        calendar.refetchEvents();
        loadStats();
        loadUpcomingHolidays();
    }
});
</script>
<?= $this->endSection() ?>
