<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        <?= $title ?? 'Academic Sessions Management' ?>
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-6">
    <!-- Total Sessions -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-calendar-alt text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="total-sessions" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Total Sessions</span>
            </div>
        </div>
    </div>

    <!-- Active Sessions -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-check-circle text-success text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="active-sessions" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Active Sessions</span>
            </div>
        </div>
    </div>

    <!-- Current Session Students -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-users text-warning text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="current-students" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Current Students</span>
            </div>
        </div>
    </div>

    <!-- Academic Years -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-graduation-cap text-info text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 id="academic-years" class="text-title-md font-bold text-black dark:text-white">0</h4>
                <span class="text-sm font-medium">Academic Years</span>
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-black dark:text-white">Session Management</h3>
            <div class="flex gap-2">
                <a href="<?= base_url($route_prefix . '/create') ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-plus mr-2"></i>
                    Create Session
                </a>
                <button onclick="archiveOldSessions()" class="inline-flex items-center justify-center rounded-md bg-warning px-4 py-2 text-center font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-archive mr-2"></i>
                    Archive Old
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <h3 class="font-medium text-black dark:text-white">Academic Sessions</h3>
    </div>
    
    <div class="p-7">
        <div class="overflow-x-auto">
            <table id="sessions-table" class="w-full table-auto">
                <thead>
                    <tr class="bg-gray-2 text-left dark:bg-meta-4">
                        <th class="min-w-[50px] py-4 px-4 font-medium text-black dark:text-white">#</th>
                        <th class="min-w-[150px] py-4 px-4 font-medium text-black dark:text-white">Session</th>
                        <th class="min-w-[120px] py-4 px-4 font-medium text-black dark:text-white">Academic Year</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">Start Date</th>
                        <th class="min-w-[100px] py-4 px-4 font-medium text-black dark:text-white">End Date</th>
                        <th class="min-w-[80px] py-4 px-4 font-medium text-black dark:text-white">Status</th>
                        <th class="min-w-[80px] py-4 px-4 font-medium text-black dark:text-white">Current</th>
                        <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via DataTables AJAX -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Load statistics
    loadStats();
    
    // Initialize DataTable
    const table = $('#sessions-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url($route_prefix . '/get-data') ?>',
            type: 'POST',
            data: function(d) {
                d[csrf_token] = csrf_hash;
            }
        },
        columns: [
            { data: 'id', name: 'id' },
            { data: 'session', name: 'session' },
            { data: 'academic_year', name: 'academic_year' },
            { data: 'start_date', name: 'start_date' },
            { data: 'end_date', name: 'end_date' },
            { data: 'is_active', name: 'is_active', orderable: false },
            { data: 'is_current', name: 'is_current', orderable: false },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[3, 'desc']], // Order by start_date desc
        pageLength: 25,
        responsive: true
    });

    // Set current session function
    window.setCurrent = function(id) {
        Swal.fire({
            title: 'Set as Current Session?',
            text: "This will make this session the current active session for all operations.",
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, set as current!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . '/set-current') ?>/' + id,
                    type: 'POST',
                    data: {
                        [csrf_token]: csrf_hash
                    }
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while setting current session', 'error');
                });
            }
        });
    };

    // Archive old sessions function
    window.archiveOldSessions = function() {
        Swal.fire({
            title: 'Archive Old Sessions',
            html: '<label>Archive sessions before:</label><input id="archive-date" type="date" class="swal2-input" value="' + getDateTwoYearsAgo() + '">',
            showCancelButton: true,
            confirmButtonText: 'Archive',
            preConfirm: () => {
                const date = document.getElementById('archive-date').value;
                if (!date) {
                    Swal.showValidationMessage('Please select a date');
                    return false;
                }
                return date;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . '/archive-old') ?>',
                    type: 'POST',
                    data: {
                        before_date: result.value,
                        [csrf_token]: csrf_hash
                    }
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('Failed to archive sessions', 'error');
                });
            }
        });
    };

    // Delete function
    window.deleteRecord = function(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this! This will also affect all related data.",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url($route_prefix . '/delete') ?>/' + id,
                    type: 'DELETE',
                    data: {
                        [csrf_token]: csrf_hash
                    }
                })
                .done(function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        table.ajax.reload();
                        loadStats();
                    } else {
                        showToast(response.message, 'error');
                    }
                })
                .fail(function() {
                    showToast('An error occurred while deleting the session', 'error');
                });
            }
        });
    };

    function loadStats() {
        $.get('<?= base_url($route_prefix . '/get-stats') ?>')
            .done(function(response) {
                if (response.success) {
                    $('#total-sessions').text(response.data.total_sessions || 0);
                    $('#active-sessions').text(response.data.active_sessions || 0);
                    $('#current-students').text(response.data.total_students || 0);
                    $('#academic-years').text(response.data.academic_years || 0);
                }
            })
            .fail(function() {
                console.log('Failed to load statistics');
            });
    }

    function getDateTwoYearsAgo() {
        const date = new Date();
        date.setFullYear(date.getFullYear() - 2);
        return date.toISOString().split('T')[0];
    }
});
</script>
<?= $this->endSection() ?>
