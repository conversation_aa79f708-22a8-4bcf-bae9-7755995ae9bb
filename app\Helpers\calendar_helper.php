<?php

use App\Services\CalendarService;

if (!function_exists('is_school_holiday')) {
    /**
     * Check if a date is a school holiday
     */
    function is_school_holiday($date, $sessionId = null, $appliesTo = 'students')
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->isSchoolHoliday($date, $sessionId, $appliesTo);
    }
}

if (!function_exists('affects_attendance')) {
    /**
     * Check if a date affects attendance
     */
    function affects_attendance($date, $sessionId = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->affectsAttendance($date, $sessionId);
    }
}

if (!function_exists('validate_attendance_date')) {
    /**
     * Validate if attendance can be marked on a date
     */
    function validate_attendance_date($date, $sessionId = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->validateAttendanceDate($date, $sessionId);
    }
}

if (!function_exists('get_working_days')) {
    /**
     * Get working days between two dates
     */
    function get_working_days($startDate, $endDate, $sessionId = null, $excludeWeekends = true)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->getWorkingDays($startDate, $endDate, $sessionId, $excludeWeekends);
    }
}

if (!function_exists('get_holiday_info')) {
    /**
     * Get holiday information for a specific date
     */
    function get_holiday_info($date, $sessionId = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->getHolidayInfo($date, $sessionId);
    }
}

if (!function_exists('get_upcoming_holidays')) {
    /**
     * Get upcoming holidays
     */
    function get_upcoming_holidays($limit = 5, $sessionId = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->getUpcomingHolidays($limit, $sessionId);
    }
}

if (!function_exists('is_school_hours')) {
    /**
     * Check if current time is within school hours
     */
    function is_school_hours($time = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->isSchoolHours($time);
    }
}

if (!function_exists('get_calendar_summary')) {
    /**
     * Get academic calendar summary
     */
    function get_calendar_summary($sessionId = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->getCalendarSummary($sessionId);
    }
}

if (!function_exists('get_attendance_safe_dates')) {
    /**
     * Get dates where attendance can be safely marked
     */
    function get_attendance_safe_dates($startDate, $endDate, $sessionId = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->getAttendanceSafeDates($startDate, $endDate, $sessionId);
    }
}

if (!function_exists('get_holiday_conflicts')) {
    /**
     * Get holiday conflicts for a date range
     */
    function get_holiday_conflicts($startDate, $endDate, $sessionId = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->getHolidayConflicts($startDate, $endDate, $sessionId);
    }
}

if (!function_exists('format_holiday_date_range')) {
    /**
     * Format holiday date range for display
     */
    function format_holiday_date_range($startDate, $endDate)
    {
        if ($startDate === $endDate) {
            return date('M d, Y', strtotime($startDate));
        }
        
        $start = date('M d', strtotime($startDate));
        $end = date('M d, Y', strtotime($endDate));
        
        // Same month
        if (date('Y-m', strtotime($startDate)) === date('Y-m', strtotime($endDate))) {
            return $start . ' - ' . $end;
        }
        
        // Different months
        return date('M d, Y', strtotime($startDate)) . ' - ' . $end;
    }
}

if (!function_exists('get_holiday_badge')) {
    /**
     * Get holiday badge HTML
     */
    function get_holiday_badge($holiday)
    {
        $badgeClass = 'inline-flex rounded-full py-1 px-3 text-sm font-medium';
        
        if ($holiday['affects_attendance']) {
            $badgeClass .= ' bg-danger bg-opacity-10 text-danger';
            $text = 'No Attendance';
        } elseif ($holiday['is_academic_break']) {
            $badgeClass .= ' bg-warning bg-opacity-10 text-warning';
            $text = 'Academic Break';
        } else {
            $badgeClass .= ' bg-info bg-opacity-10 text-info';
            $text = 'School Event';
        }
        
        return '<span class="' . $badgeClass . '">' . $text . '</span>';
    }
}

if (!function_exists('get_day_type')) {
    /**
     * Get day type (working day, weekend, holiday)
     */
    function get_day_type($date, $sessionId = null)
    {
        $dayOfWeek = date('w', strtotime($date));
        
        // Check weekend
        if ($dayOfWeek == 0 || $dayOfWeek == 6) {
            return [
                'type' => 'weekend',
                'label' => 'Weekend',
                'class' => 'text-muted',
                'can_mark_attendance' => false
            ];
        }
        
        // Check holiday
        if (is_school_holiday($date, $sessionId)) {
            $holidayInfo = get_holiday_info($date, $sessionId);
            
            if (affects_attendance($date, $sessionId)) {
                return [
                    'type' => 'holiday_no_attendance',
                    'label' => 'Holiday',
                    'class' => 'text-danger',
                    'can_mark_attendance' => false,
                    'holiday_info' => $holidayInfo
                ];
            } else {
                return [
                    'type' => 'holiday_with_attendance',
                    'label' => 'Holiday (Attendance Allowed)',
                    'class' => 'text-warning',
                    'can_mark_attendance' => true,
                    'holiday_info' => $holidayInfo
                ];
            }
        }
        
        // Regular working day
        return [
            'type' => 'working_day',
            'label' => 'Working Day',
            'class' => 'text-success',
            'can_mark_attendance' => true
        ];
    }
}

if (!function_exists('generate_calendar_grid')) {
    /**
     * Generate calendar grid for a month
     */
    function generate_calendar_grid($year, $month, $sessionId = null)
    {
        $firstDay = mktime(0, 0, 0, $month, 1, $year);
        $daysInMonth = date('t', $firstDay);
        $startDayOfWeek = date('w', $firstDay);
        
        $calendar = [];
        $week = [];
        
        // Add empty cells for days before month starts
        for ($i = 0; $i < $startDayOfWeek; $i++) {
            $week[] = null;
        }
        
        // Add days of the month
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $date = sprintf('%04d-%02d-%02d', $year, $month, $day);
            $dayType = get_day_type($date, $sessionId);
            
            $week[] = [
                'day' => $day,
                'date' => $date,
                'type' => $dayType['type'],
                'label' => $dayType['label'],
                'class' => $dayType['class'],
                'can_mark_attendance' => $dayType['can_mark_attendance'],
                'holiday_info' => $dayType['holiday_info'] ?? null
            ];
            
            // Start new week on Sunday
            if (count($week) == 7) {
                $calendar[] = $week;
                $week = [];
            }
        }
        
        // Fill remaining cells in last week
        while (count($week) < 7) {
            $week[] = null;
        }
        
        if (!empty($week)) {
            $calendar[] = $week;
        }
        
        return $calendar;
    }
}

if (!function_exists('get_month_holidays')) {
    /**
     * Get holidays for a specific month
     */
    function get_month_holidays($year, $month, $sessionId = null)
    {
        $calendarService = CalendarService::getInstance();
        return $calendarService->getCalendarData($year, $month, $sessionId);
    }
}

if (!function_exists('count_working_days_in_month')) {
    /**
     * Count working days in a month
     */
    function count_working_days_in_month($year, $month, $sessionId = null)
    {
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));
        
        $workingDays = get_working_days($startDate, $endDate, $sessionId);
        return count($workingDays);
    }
}

if (!function_exists('get_next_working_day')) {
    /**
     * Get next working day after a given date
     */
    function get_next_working_day($date, $sessionId = null)
    {
        $currentDate = strtotime($date . ' +1 day');
        
        while (true) {
            $dateString = date('Y-m-d', $currentDate);
            $dayType = get_day_type($dateString, $sessionId);
            
            if ($dayType['can_mark_attendance']) {
                return $dateString;
            }
            
            $currentDate = strtotime('+1 day', $currentDate);
            
            // Prevent infinite loop (max 30 days ahead)
            if ($currentDate > strtotime($date . ' +30 days')) {
                return null;
            }
        }
    }
}

if (!function_exists('get_previous_working_day')) {
    /**
     * Get previous working day before a given date
     */
    function get_previous_working_day($date, $sessionId = null)
    {
        $currentDate = strtotime($date . ' -1 day');
        
        while (true) {
            $dateString = date('Y-m-d', $currentDate);
            $dayType = get_day_type($dateString, $sessionId);
            
            if ($dayType['can_mark_attendance']) {
                return $dateString;
            }
            
            $currentDate = strtotime('-1 day', $currentDate);
            
            // Prevent infinite loop (max 30 days back)
            if ($currentDate < strtotime($date . ' -30 days')) {
                return null;
            }
        }
    }
}
