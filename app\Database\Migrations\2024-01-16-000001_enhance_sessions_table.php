<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class EnhanceSessionsTable extends Migration
{
    public function up()
    {
        // Add new fields to sessions table
        $fields = [
            'academic_year' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
                'comment' => 'Academic year (e.g., 2024-2025)',
                'after' => 'session'
            ],
            'start_date' => [
                'type' => 'DATE',
                'null' => true,
                'comment' => 'Session start date',
                'after' => 'academic_year'
            ],
            'end_date' => [
                'type' => 'DATE',
                'null' => true,
                'comment' => 'Session end date',
                'after' => 'start_date'
            ],
            'is_current' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
                'default' => 'no',
                'comment' => 'Is this the current active session',
                'after' => 'is_active'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Session description',
                'after' => 'is_current'
            ]
        ];
        
        $this->forge->addColumn('sessions', $fields);

        // Add indexes for better performance
        $this->forge->addKey('sessions', 'academic_year');
        $this->forge->addKey('sessions', 'start_date');
        $this->forge->addKey('sessions', 'end_date');
        $this->forge->addKey('sessions', 'is_current');

        // Update existing sessions with default values
        $this->updateExistingSessions();
    }

    public function down()
    {
        $this->forge->dropColumn('sessions', [
            'academic_year', 
            'start_date', 
            'end_date', 
            'is_current', 
            'description'
        ]);
    }

    /**
     * Update existing sessions with default academic year and dates
     */
    private function updateExistingSessions()
    {
        $db = \Config\Database::connect();
        
        // Get all existing sessions
        $sessions = $db->table('sessions')->get()->getResultArray();
        
        foreach ($sessions as $session) {
            // Extract year from session name (assuming format like "2024-1", "2024-2", etc.)
            $sessionName = $session['session'];
            $academicYear = $this->extractAcademicYear($sessionName);
            
            // Set default dates based on session pattern
            $dates = $this->generateSessionDates($sessionName);
            
            // Update the session
            $db->table('sessions')
               ->where('id', $session['id'])
               ->update([
                   'academic_year' => $academicYear,
                   'start_date' => $dates['start_date'],
                   'end_date' => $dates['end_date'],
                   'is_current' => $session['is_active'], // Current active becomes current
                   'description' => "Academic session {$sessionName}"
               ]);
        }
    }

    /**
     * Extract academic year from session name
     */
    private function extractAcademicYear($sessionName)
    {
        // Try to extract year from session name
        if (preg_match('/(\d{4})/', $sessionName, $matches)) {
            $year = (int)$matches[1];
            return $year . '-' . ($year + 1);
        }
        
        // Default to current academic year
        $currentYear = date('Y');
        return $currentYear . '-' . ($currentYear + 1);
    }

    /**
     * Generate session dates based on session name
     */
    private function generateSessionDates($sessionName)
    {
        // Extract year and semester/term
        if (preg_match('/(\d{4})(\d)/', $sessionName, $matches)) {
            $year = (int)$matches[1];
            $term = (int)$matches[2];
            
            if ($term == 1) {
                // First semester: July to December
                return [
                    'start_date' => $year . '-07-01',
                    'end_date' => $year . '-12-31'
                ];
            } else {
                // Second semester: January to June of next year
                return [
                    'start_date' => ($year + 1) . '-01-01',
                    'end_date' => ($year + 1) . '-06-30'
                ];
            }
        }
        
        // Default to full academic year
        $currentYear = date('Y');
        return [
            'start_date' => $currentYear . '-07-01',
            'end_date' => ($currentYear + 1) . '-06-30'
        ];
    }
}
