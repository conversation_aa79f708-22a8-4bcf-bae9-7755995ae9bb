<?php

namespace App\Models;

use CodeIgniter\Model;

class HolidayTypesModel extends Model
{
    protected $table = 'holiday_types';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'name', 'description', 'color', 'is_academic_break', 
        'affects_attendance', 'is_active'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[100]|is_unique[holiday_types.name,id,{id}]',
        'color' => 'required|regex_match[/^#[0-9A-Fa-f]{6}$/]',
        'is_academic_break' => 'permit_empty|in_list[0,1]',
        'affects_attendance' => 'permit_empty|in_list[0,1]',
        'is_active' => 'permit_empty|in_list[yes,no]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Holiday type name is required',
            'min_length' => 'Holiday type name must be at least 3 characters long',
            'max_length' => 'Holiday type name cannot exceed 100 characters',
            'is_unique' => 'Holiday type name already exists'
        ],
        'color' => [
            'required' => 'Color is required',
            'regex_match' => 'Color must be a valid hex color code (e.g., #007bff)'
        ]
    ];

    /**
     * Get active holiday types
     */
    public function getActive()
    {
        return $this->where('is_active', 'yes')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get holiday types for dropdown
     */
    public function getForDropdown()
    {
        $types = $this->getActive();
        
        $dropdown = [];
        foreach ($types as $type) {
            $dropdown[$type['id']] = $type['name'];
        }
        
        return $dropdown;
    }

    /**
     * Get holiday types that affect attendance
     */
    public function getAttendanceAffecting()
    {
        return $this->where('is_active', 'yes')
                   ->where('affects_attendance', 1)
                   ->findAll();
    }

    /**
     * Get academic break types
     */
    public function getAcademicBreakTypes()
    {
        return $this->where('is_active', 'yes')
                   ->where('is_academic_break', 1)
                   ->findAll();
    }

    /**
     * Get holiday type statistics
     */
    public function getStatistics()
    {
        $stats = [
            'total_types' => $this->countAllResults(),
            'active_types' => $this->where('is_active', 'yes')->countAllResults(),
            'academic_break_types' => $this->where('is_academic_break', 1)->where('is_active', 'yes')->countAllResults(),
            'attendance_affecting_types' => $this->where('affects_attendance', 1)->where('is_active', 'yes')->countAllResults()
        ];

        return $stats;
    }

    /**
     * Get holiday type with usage count
     */
    public function getWithUsageCount()
    {
        $builder = $this->db->table($this->table);
        $builder->select('holiday_types.*, COUNT(academic_calendar.id) as usage_count')
                ->join('academic_calendar', 'holiday_types.id = academic_calendar.holiday_type_id', 'left')
                ->groupBy('holiday_types.id')
                ->orderBy('holiday_types.name', 'ASC');

        return $builder->get()->getResultArray();
    }

    /**
     * Check if holiday type can be deleted
     */
    public function canDelete($id)
    {
        $usageCount = $this->db->table('academic_calendar')
                              ->where('holiday_type_id', $id)
                              ->countAllResults();

        return $usageCount === 0;
    }

    /**
     * Get color palette for holiday types
     */
    public function getColorPalette()
    {
        return [
            '#dc3545' => 'Red (National Holidays)',
            '#28a745' => 'Green (Religious)',
            '#007bff' => 'Blue (Academic)',
            '#ffc107' => 'Yellow (Examinations)',
            '#17a2b8' => 'Cyan (Events)',
            '#6f42c1' => 'Purple (Staff)',
            '#fd7e14' => 'Orange (Emergency)',
            '#20c997' => 'Teal (Special)',
            '#e83e8c' => 'Pink (Cultural)',
            '#6c757d' => 'Gray (Other)'
        ];
    }

    /**
     * Validate color uniqueness
     */
    public function isColorUnique($color, $excludeId = null)
    {
        $builder = $this->where('color', $color);
        
        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }
        
        return $builder->countAllResults() === 0;
    }

    /**
     * Get default holiday types for new installations
     */
    public function getDefaultTypes()
    {
        return [
            [
                'name' => 'Independence Day',
                'description' => 'National Independence Day celebration',
                'color' => '#dc3545',
                'is_academic_break' => 1,
                'affects_attendance' => 1
            ],
            [
                'name' => 'Eid Festival',
                'description' => 'Islamic religious festival',
                'color' => '#28a745',
                'is_academic_break' => 1,
                'affects_attendance' => 1
            ],
            [
                'name' => 'Christmas Holiday',
                'description' => 'Christmas celebration',
                'color' => '#28a745',
                'is_academic_break' => 1,
                'affects_attendance' => 1
            ],
            [
                'name' => 'Summer Vacation',
                'description' => 'Annual summer break',
                'color' => '#007bff',
                'is_academic_break' => 1,
                'affects_attendance' => 1
            ],
            [
                'name' => 'Mid-term Break',
                'description' => 'Semester break',
                'color' => '#007bff',
                'is_academic_break' => 1,
                'affects_attendance' => 1
            ]
        ];
    }

    /**
     * Bulk create default holiday types
     */
    public function createDefaultTypes($sessionId)
    {
        $defaultTypes = $this->getDefaultTypes();
        $createdCount = 0;

        foreach ($defaultTypes as $type) {
            // Check if type already exists
            $existing = $this->where('name', $type['name'])->first();
            
            if (!$existing) {
                $type['is_active'] = 'yes';
                if ($this->insert($type)) {
                    $createdCount++;
                }
            }
        }

        return [
            'success' => true,
            'created_count' => $createdCount,
            'message' => "Created {$createdCount} default holiday types"
        ];
    }
}
