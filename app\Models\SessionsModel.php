<?php

namespace App\Models;

class SessionsModel extends BaseModel
{
    protected $table = 'sessions';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'session', 'academic_year', 'start_date', 'end_date',
        'is_active', 'is_current', 'description'
    ];

    protected $validationRules = [
        'session' => 'required|min_length[4]|max_length[100]|is_unique[sessions.session,id,{id}]',
        'academic_year' => 'required|min_length[4]|max_length[20]',
        'start_date' => 'required|valid_date',
        'end_date' => 'required|valid_date',
        'is_active' => 'permit_empty|in_list[yes,no]',
        'is_current' => 'permit_empty|in_list[yes,no]'
    ];

    protected $validationMessages = [
        'session' => [
            'required' => 'Session is required',
            'min_length' => 'Session must be at least 4 characters long',
            'max_length' => 'Session cannot exceed 100 characters',
            'is_unique' => 'Session already exists'
        ],
        'academic_year' => [
            'required' => 'Academic year is required',
            'min_length' => 'Academic year must be at least 4 characters long',
            'max_length' => 'Academic year cannot exceed 20 characters'
        ],
        'start_date' => [
            'required' => 'Start date is required',
            'valid_date' => 'Please enter a valid start date'
        ],
        'end_date' => [
            'required' => 'End date is required',
            'valid_date' => 'Please enter a valid end date'
        ]
    ];

    protected $searchableColumns = ['session', 'academic_year', 'description'];
    protected $orderableColumns = ['id', 'session', 'academic_year', 'start_date', 'end_date', 'is_active', 'is_current', 'created_at'];

    /**
     * Get current active session
     */
    public function getCurrentSession()
    {
        return $this->where('is_current', 'yes')
                   ->where('is_active', 'yes')
                   ->first();
    }

    /**
     * Get active sessions
     */
    public function getActiveSessions()
    {
        return $this->where('is_active', 'yes')
                   ->orderBy('start_date', 'DESC')
                   ->findAll();
    }

    /**
     * Set current session
     */
    public function setCurrentSession($sessionId)
    {
        $this->db->transStart();

        // Remove current flag from all sessions
        $this->set('is_current', 'no')->update();

        // Set new current session
        $result = $this->update($sessionId, ['is_current' => 'yes']);

        $this->db->transComplete();

        if ($this->db->transStatus() === false) {
            return [
                'success' => false,
                'message' => 'Failed to set current session'
            ];
        }

        return [
            'success' => true,
            'message' => 'Current session updated successfully'
        ];
    }

    /**
     * Get sessions for dropdown
     */
    public function getForDropdown()
    {
        $sessions = $this->where('is_active', 'yes')
                        ->orderBy('session', 'DESC')
                        ->findAll();
        
        $dropdown = [];
        foreach ($sessions as $session) {
            $dropdown[$session['id']] = $session['session'];
        }
        
        return $dropdown;
    }

    /**
     * Get all sessions for dropdown (including inactive)
     */
    public function getAllForDropdown()
    {
        $sessions = $this->orderBy('start_date', 'DESC')->findAll();

        $dropdown = [];
        foreach ($sessions as $session) {
            $status = $session['is_active'] === 'yes' ? '' : ' (Inactive)';
            $current = $session['is_current'] === 'yes' ? ' (Current)' : '';
            $dropdown[$session['id']] = $session['session'] . $current . $status;
        }

        return $dropdown;
    }

    /**
     * Create new academic session
     */
    public function createAcademicSession($data)
    {
        // Validate date range
        if (strtotime($data['start_date']) >= strtotime($data['end_date'])) {
            return [
                'success' => false,
                'message' => 'End date must be after start date'
            ];
        }

        // Set defaults
        $data['is_active'] = $data['is_active'] ?? 'yes';
        $data['is_current'] = $data['is_current'] ?? 'no';

        // If this is set as current, remove current flag from others
        if ($data['is_current'] === 'yes') {
            $this->set('is_current', 'no')->update();
        }

        $result = $this->insert($data);

        if ($result) {
            return [
                'success' => true,
                'message' => 'Academic session created successfully',
                'data' => $this->find($this->getInsertID())
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create academic session'
        ];
    }

    /**
     * Set active session
     */
    public function setActiveSession($sessionId)
    {
        // First, deactivate all sessions
        $this->set('is_active', 'no')->update();

        // Then activate the selected session
        return $this->update($sessionId, ['is_active' => 'yes']);
    }

    /**
     * Get session statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total sessions
        $stats['total'] = $this->countAllResults();
        
        // Active sessions
        $stats['active'] = $this->where('is_active', 'yes')->countAllResults();
        
        return $stats;
    }
}
