<?php

namespace App\Controllers;

use App\Models\SessionsModel;

class SessionsController extends BaseCrudController
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new SessionsModel();
        
        $this->viewPath = 'admin/sessions';
        $this->routePrefix = 'admin/sessions';
        $this->entityName = 'Academic Session';
        $this->entityNamePlural = 'Academic Sessions';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Show create form
     */
    public function create()
    {
        $data = [
            'title' => 'Create ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Create', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/create', $data);
    }

    /**
     * Store new session
     */
    public function store()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        
        // Use the enhanced create method
        $result = $this->model->createAcademicSession($data);

        return $this->response->setJSON($result);
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $record = $this->model->find($id);
        
        if (!$record) {
            return redirect()->to($this->routePrefix)->with('error', $this->entityName . ' not found');
        }

        $data = [
            'title' => 'Edit ' . $this->entityName,
            'entity_name' => $this->entityName,
            'route_prefix' => $this->routePrefix,
            'record' => $record,
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => $this->entityNamePlural, 'url' => base_url($this->routePrefix)],
                ['name' => 'Edit', 'url' => '']
            ]
        ];

        return view($this->viewPath . '/edit', $data);
    }

    /**
     * Update session
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        
        // Validate date range
        if (strtotime($data['start_date']) >= strtotime($data['end_date'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'End date must be after start date'
            ]);
        }

        // If setting as current, remove current flag from others
        if (isset($data['is_current']) && $data['is_current'] === 'yes') {
            $this->model->set('is_current', 'no')->update();
        }

        $result = $this->model->update($id, $data);

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => $this->entityName . ' updated successfully'
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to update ' . $this->entityName,
            'errors' => $this->model->errors()
        ]);
    }

    /**
     * Set current session
     */
    public function setCurrent($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $result = $this->model->setCurrentSession($id);
        return $this->response->setJSON($result);
    }

    /**
     * Get session statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $sessionId = $this->request->getGet('session_id');

        if ($sessionId) {
            // Get specific session stats
            $stats = $this->model->getSessionStatistics($sessionId);
        } else {
            // Get dashboard stats
            $totalSessions = $this->model->countAllResults();
            $activeSessions = $this->model->where('is_active', 'yes')->countAllResults();

            $currentSession = $this->model->getCurrentSession();
            $currentStudents = 0;
            if ($currentSession) {
                $currentStudents = $this->db->table('student_session')
                                           ->where('session_id', $currentSession['id'])
                                           ->where('is_active', 'yes')
                                           ->countAllResults();
            }

            $academicYears = $this->model->select('academic_year')
                                       ->distinct()
                                       ->where('academic_year IS NOT NULL')
                                       ->countAllResults();

            $stats = [
                'total_sessions' => $totalSessions,
                'active_sessions' => $activeSessions,
                'total_students' => $currentStudents,
                'academic_years' => $academicYears
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Archive old sessions
     */
    public function archiveOld()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $beforeDate = $this->request->getPost('before_date');
        $result = $this->model->archiveOldSessions($beforeDate);

        return $this->response->setJSON($result);
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start') ?? 0;
        $length = $this->request->getPost('length') ?? 10;
        $searchValue = $this->request->getPost('search')['value'] ?? '';

        $builder = $this->model->builder();

        // Apply search
        if (!empty($searchValue)) {
            $builder->groupStart()
                    ->like('session', $searchValue)
                    ->orLike('academic_year', $searchValue)
                    ->orLike('description', $searchValue)
                    ->groupEnd();
        }

        // Get total records
        $totalRecords = $this->model->countAllResults(false);
        $filteredRecords = $builder->countAllResults(false);

        // Apply pagination and ordering
        $builder->orderBy('start_date', 'DESC')
                ->limit($length, $start);

        $records = $builder->get()->getResultArray();

        $data = [];
        foreach ($records as $record) {
            $data[] = [
                'id' => $record['id'],
                'session' => $record['session'],
                'academic_year' => $record['academic_year'] ?? 'N/A',
                'start_date' => $record['start_date'] ? date('M d, Y', strtotime($record['start_date'])) : 'N/A',
                'end_date' => $record['end_date'] ? date('M d, Y', strtotime($record['end_date'])) : 'N/A',
                'is_active' => $this->getStatusBadge($record['is_active']),
                'is_current' => $this->getCurrentBadge($record['is_current']),
                'actions' => $this->getActionButtons($record)
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Get status badge HTML
     */
    private function getStatusBadge($status)
    {
        if ($status === 'yes') {
            return '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Active</span>';
        } else {
            return '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Inactive</span>';
        }
    }

    /**
     * Get current badge HTML
     */
    private function getCurrentBadge($isCurrent)
    {
        if ($isCurrent === 'yes') {
            return '<span class="inline-flex rounded-full bg-primary bg-opacity-10 py-1 px-3 text-sm font-medium text-primary">Current</span>';
        } else {
            return '<span class="inline-flex rounded-full bg-gray bg-opacity-10 py-1 px-3 text-sm font-medium text-gray">-</span>';
        }
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        if ($record['is_current'] !== 'yes') {
            $buttons .= '<button onclick="setCurrent(' . $record['id'] . ')" class="hover:text-success" title="Set as Current">
                            <i class="fas fa-check-circle"></i>
                        </button>';
        }

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }

    /**
     * Switch session context
     */
    public function switchContext()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $sessionId = $this->request->getPost('session_id');

        if (!$sessionId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Session ID is required'
            ]);
        }

        // Validate session
        $session = $this->model->find($sessionId);
        if (!$session || $session['is_active'] !== 'yes') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid or inactive session'
            ]);
        }

        // Set session context
        session()->set('current_session_id', $sessionId);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Session context switched successfully',
            'session' => $session
        ]);
    }

    /**
     * Clear session context
     */
    public function clearContext()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        session()->remove('current_session_id');

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Session context cleared'
        ]);
    }
}
