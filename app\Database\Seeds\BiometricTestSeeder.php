<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class BiometricTestSeeder extends Seeder
{
    public function run()
    {
        // First, let's add biometric PINs to existing students
        $studentsData = [
            ['id' => 1, 'biometric_pin' => '1111'],
            ['id' => 2, 'biometric_pin' => '1112'],
            ['id' => 3, 'biometric_pin' => '1113']
        ];

        foreach ($studentsData as $student) {
            $this->db->table('students')
                     ->where('id', $student['id'])
                     ->update(['biometric_pin' => $student['biometric_pin']]);
        }

        // Now let's create sample biometric attendance data
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        
        $attLogData = [
            // Today's attendance - Student 1
            [
                'att_id' => 'ATT001_' . date('YmdHis'),
                'pin' => '1111',
                'scan_date' => $today . ' 07:25:00',
                'verifymode' => 1, // Fingerprint
                'status' => 1, // Present
                'serialnumber' => 'FP001',
                'student_id' => 1,
                'sn' => 'DEVICE001',
                'inoutmode' => 0, // Check In
                'reserved' => 0,
                'work_code' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'att_id' => 'ATT002_' . date('YmdHis'),
                'pin' => '1111',
                'scan_date' => $today . ' 14:35:00',
                'verifymode' => 1, // Fingerprint
                'status' => 1, // Present
                'serialnumber' => 'FP001',
                'student_id' => 1,
                'sn' => 'DEVICE001',
                'inoutmode' => 2, // Check Out
                'reserved' => 0,
                'work_code' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            
            // Today's attendance - Student 2 (Late arrival)
            [
                'att_id' => 'ATT003_' . date('YmdHis'),
                'pin' => '1112',
                'scan_date' => $today . ' 08:15:00',
                'verifymode' => 1, // Fingerprint
                'status' => 2, // Late
                'serialnumber' => 'FP001',
                'student_id' => 2,
                'sn' => 'DEVICE001',
                'inoutmode' => 0, // Check In
                'reserved' => 0,
                'work_code' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'att_id' => 'ATT004_' . date('YmdHis'),
                'pin' => '1112',
                'scan_date' => $today . ' 14:30:00',
                'verifymode' => 1, // Fingerprint
                'status' => 1, // Present
                'serialnumber' => 'FP001',
                'student_id' => 2,
                'sn' => 'DEVICE001',
                'inoutmode' => 2, // Check Out
                'reserved' => 0,
                'work_code' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],

            // Yesterday's attendance - Student 1
            [
                'att_id' => 'ATT005_' . date('YmdHis'),
                'pin' => '1111',
                'scan_date' => $yesterday . ' 07:30:00',
                'verifymode' => 1, // Fingerprint
                'status' => 1, // Present
                'serialnumber' => 'FP001',
                'student_id' => 1,
                'sn' => 'DEVICE001',
                'inoutmode' => 0, // Check In
                'reserved' => 0,
                'work_code' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'att_id' => 'ATT006_' . date('YmdHis'),
                'pin' => '1111',
                'scan_date' => $yesterday . ' 14:25:00',
                'verifymode' => 1, // Fingerprint
                'status' => 1, // Present
                'serialnumber' => 'FP001',
                'student_id' => 1,
                'sn' => 'DEVICE001',
                'inoutmode' => 2, // Check Out
                'reserved' => 0,
                'work_code' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],

            // Sample unlinked record (no student_id)
            [
                'att_id' => 'ATT007_' . date('YmdHis'),
                'pin' => '9999', // Unknown PIN
                'scan_date' => $today . ' 07:45:00',
                'verifymode' => 1, // Fingerprint
                'status' => 1, // Present
                'serialnumber' => 'FP001',
                'student_id' => null, // Unlinked
                'sn' => 'DEVICE001',
                'inoutmode' => 0, // Check In
                'reserved' => 0,
                'work_code' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insert the attendance log data
        $this->db->table('att_log')->insertBatch($attLogData);

        echo "Biometric test data seeded successfully!\n";
        echo "- Updated " . count($studentsData) . " students with biometric PINs\n";
        echo "- Created " . count($attLogData) . " attendance log records\n";
        echo "- Use 'php spark attendance:sync' to process the data\n";
    }
}
