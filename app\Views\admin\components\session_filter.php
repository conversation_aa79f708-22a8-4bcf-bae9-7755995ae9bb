<?php
// Load session helper
helper('session');

// Get session filter data
$sessionFilter = get_session_filter();
$currentSession = $sessionFilter['current_session'];
$allSessions = $sessionFilter['all_sessions'];
$selectedSessionId = $sessionFilter['selected_session_id'];
?>

<!-- Session Context Filter -->
<div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mb-6">
    <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <h3 class="font-medium text-black dark:text-white">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Academic Session Context
                </h3>
                
                <?php if ($currentSession): ?>
                    <div class="flex items-center gap-2">
                        <?= get_session_status_badge($currentSession) ?>
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            <?= format_academic_year($currentSession['academic_year']) ?>
                        </span>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="flex items-center gap-3">
                <!-- Session Selector -->
                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-black dark:text-white">View Session:</label>
                    <select id="session-context-selector" class="rounded border border-stroke bg-gray py-2 px-3 text-black focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:text-white">
                        <option value="">Current Session</option>
                        <?php foreach ($allSessions as $sessionId => $sessionName): ?>
                            <option value="<?= $sessionId ?>" <?= $selectedSessionId == $sessionId ? 'selected' : '' ?>>
                                <?= esc($sessionName) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Session Management Link -->
                <a href="<?= base_url('admin/sessions') ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-3 py-2 text-center text-sm font-medium text-white hover:bg-opacity-90">
                    <i class="fas fa-cog mr-1"></i>
                    Manage
                </a>
            </div>
        </div>
    </div>
    
    <!-- Session Details -->
    <?php if ($currentSession): ?>
        <div class="py-3 px-7">
            <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
                <div>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Session:</span>
                    <p class="font-medium text-black dark:text-white"><?= esc($currentSession['session']) ?></p>
                </div>
                
                <div>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Academic Year:</span>
                    <p class="font-medium text-black dark:text-white"><?= format_academic_year($currentSession['academic_year']) ?></p>
                </div>
                
                <div>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Duration:</span>
                    <p class="font-medium text-black dark:text-white">
                        <?php 
                        $dateRange = get_session_date_range($currentSession['id']);
                        echo $dateRange ? $dateRange['formatted_range'] : 'N/A';
                        ?>
                    </p>
                </div>
                
                <div>
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
                    <p class="font-medium text-black dark:text-white">
                        <?= $currentSession['is_current'] === 'yes' ? 'Current Session' : 'Historical Session' ?>
                    </p>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="py-4 px-7">
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-warning text-2xl mb-2"></i>
                <p class="text-gray-600 dark:text-gray-400">No active session found. Please create and activate a session.</p>
                <a href="<?= base_url('admin/sessions/create') ?>" class="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-center font-medium text-white hover:bg-opacity-90 mt-3">
                    <i class="fas fa-plus mr-2"></i>
                    Create Session
                </a>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
$(document).ready(function() {
    // Session context selector
    $('#session-context-selector').on('change', function() {
        const sessionId = $(this).val();
        
        if (sessionId) {
            // Switch to selected session context
            $.ajax({
                url: '<?= base_url('admin/sessions/switch-context') ?>',
                type: 'POST',
                data: {
                    session_id: sessionId,
                    [csrf_token]: csrf_hash
                }
            })
            .done(function(response) {
                if (response.success) {
                    // Reload page to apply new session context
                    window.location.reload();
                } else {
                    showToast(response.message, 'error');
                    // Reset selector
                    $('#session-context-selector').val('');
                }
            })
            .fail(function() {
                showToast('Failed to switch session context', 'error');
                $('#session-context-selector').val('');
            });
        } else {
            // Clear session context (revert to current)
            $.ajax({
                url: '<?= base_url('admin/sessions/clear-context') ?>',
                type: 'POST',
                data: {
                    [csrf_token]: csrf_hash
                }
            })
            .done(function(response) {
                if (response.success) {
                    window.location.reload();
                } else {
                    showToast(response.message, 'error');
                }
            })
            .fail(function() {
                showToast('Failed to clear session context', 'error');
            });
        }
    });
});
</script>
