<?php

namespace App\Models;

class StudentAttendanceModel extends BaseModel
{
    protected $table = 'student_attendences';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'student_session_id', 'biometric_attendence', 'qrcode_attendance', 'date',
        'attendence_type_id', 'remark', 'biometric_device_data', 'user_agent'
    ];

    protected $validationRules = [
        'student_session_id' => 'required|integer',
        'date' => 'required|valid_date',
        'attendence_type_id' => 'required|integer'
    ];

    protected $validationMessages = [
        'student_session_id' => [
            'required' => 'Student session is required',
            'integer' => 'Invalid student session'
        ],
        'date' => [
            'required' => 'Date is required',
            'valid_date' => 'Please enter a valid date'
        ],
        'attendence_type_id' => [
            'required' => 'Attendance type is required',
            'integer' => 'Invalid attendance type'
        ]
    ];

    protected $searchableColumns = ['date', 'remark'];
    protected $orderableColumns = ['id', 'date', 'attendence_type_id', 'created_at'];

    // Attendance types based on timing and context
    const ATTENDANCE_TYPE_PRESENT = 1;
    const ATTENDANCE_TYPE_ABSENT = 2;
    const ATTENDANCE_TYPE_LATE = 3;
    const ATTENDANCE_TYPE_HALF_DAY = 4;
    const ATTENDANCE_TYPE_PERMISSION = 5;

    // School timing constants (can be moved to config)
    const SCHOOL_START_TIME = '07:30:00';
    const SCHOOL_END_TIME = '14:30:00';
    const LATE_THRESHOLD_MINUTES = 15;

    /**
     * Get attendance with student details
     */
    public function getAttendanceWithDetails($filters = [])
    {
        helper('session');

        $builder = $this->db->table($this->table);
        $builder->select('student_attendences.*, students.firstname, students.lastname, students.admission_no,
                         students.roll_no, classes.class, sections.section, attendence_type.type as attendance_type,
                         sessions.session as session_name, sessions.academic_year')
                ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('sessions', 'student_session.session_id = sessions.id')
                ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id');

        // Apply session filter first
        $sessionId = $filters['session_id'] ?? session()->get('current_session_id') ?? get_current_session_id();
        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }

        // Apply other filters
        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['date'])) {
            $builder->where('student_attendences.date', $filters['date']);
        }

        if (!empty($filters['attendance_type'])) {
            $builder->where('attendence_type.type', $filters['attendance_type']);
        }

        if (!empty($filters['source'])) {
            if ($filters['source'] === 'biometric') {
                $builder->where('student_attendences.biometric_attendence', 1);
            } elseif ($filters['source'] === 'manual') {
                $builder->where('student_attendences.biometric_attendence !=', 1);
            }
        }

        if (!empty($filters['date_from'])) {
            $builder->where('student_attendences.date >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('student_attendences.date <=', $filters['date_to']);
        }

        if (!empty($filters['attendance_type_id'])) {
            $builder->where('student_attendences.attendence_type_id', $filters['attendance_type_id']);
        }

        return $builder->orderBy('student_attendences.date', 'DESC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Mark attendance for multiple students
     */
    public function markBulkAttendance($attendanceData, $date)
    {
        $this->db->transStart();

        try {
            $markedCount = 0;

            foreach ($attendanceData as $studentSessionId => $attendanceTypeId) {
                // Check if attendance already exists for this date
                $existing = $this->where('student_session_id', $studentSessionId)
                               ->where('date', $date)
                               ->first();

                $data = [
                    'student_session_id' => $studentSessionId,
                    'date' => $date,
                    'attendence_type_id' => $attendanceTypeId,
                    'biometric_attendence' => 0,
                    'qrcode_attendance' => 0,
                    'remark' => '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
                ];

                if ($existing) {
                    // Update existing attendance
                    $this->update($existing['id'], $data);
                } else {
                    // Insert new attendance
                    $this->insert($data);
                }

                $markedCount++;
            }

            $this->db->transComplete();

            if ($this->db->transStatus() === false) {
                return [
                    'success' => false,
                    'message' => 'Failed to mark attendance',
                    'data' => null
                ];
            }

            return [
                'success' => true,
                'message' => "Attendance marked for $markedCount students",
                'data' => ['marked_count' => $markedCount]
            ];

        } catch (\Exception $e) {
            $this->db->transRollback();
            return [
                'success' => false,
                'message' => 'Error occurred while marking attendance: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get attendance summary for a student
     */
    public function getStudentAttendanceSummary($studentId, $month = null, $year = null)
    {
        $month = $month ?: date('m');
        $year = $year ?: date('Y');

        return $this->db->table($this->table)
                       ->select('attendence_type.type, COUNT(*) as count')
                       ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                       ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id')
                       ->where('student_session.student_id', $studentId)
                       ->where('MONTH(student_attendences.date)', $month)
                       ->where('YEAR(student_attendences.date)', $year)
                       ->groupBy('attendence_type.id')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get class attendance summary
     */
    public function getClassAttendanceSummary($classId, $sectionId, $date)
    {
        return $this->db->table($this->table)
                       ->select('attendence_type.type, COUNT(*) as count')
                       ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                       ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id')
                       ->where('student_session.class_id', $classId)
                       ->where('student_session.section_id', $sectionId)
                       ->where('student_attendences.date', $date)
                       ->groupBy('attendence_type.id')
                       ->get()
                       ->getResultArray();
    }

    /**
     * Get monthly attendance report
     */
    public function getMonthlyAttendanceReport($classId = null, $sectionId = null, $month = null, $year = null)
    {
        $month = $month ?: date('m');
        $year = $year ?: date('Y');

        $builder = $this->db->table($this->table);
        $builder->select('DAY(student_attendences.date) as day, 
                         attendence_type.type, 
                         COUNT(*) as count')
                ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id')
                ->where('MONTH(student_attendences.date)', $month)
                ->where('YEAR(student_attendences.date)', $year);

        if ($classId) {
            $builder->where('student_session.class_id', $classId);
        }

        if ($sectionId) {
            $builder->where('student_session.section_id', $sectionId);
        }

        return $builder->groupBy('DAY(student_attendences.date), attendence_type.id')
                      ->orderBy('day', 'ASC')
                      ->get()
                      ->getResultArray();
    }

    /**
     * Get attendance statistics
     */
    public function getStatistics()
    {
        $stats = [];
        
        // Total attendance records
        $stats['total'] = $this->countAllResults();
        
        // Today's attendance
        $stats['today'] = $this->where('date', date('Y-m-d'))->countAllResults();
        
        // This month's attendance
        $stats['this_month'] = $this->where('MONTH(date)', date('m'))
                                   ->where('YEAR(date)', date('Y'))
                                   ->countAllResults();
        
        // Attendance by type (today)
        $stats['by_type_today'] = $this->db->table($this->table)
                                          ->select('attendence_type.type, COUNT(*) as count')
                                          ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id')
                                          ->where('student_attendences.date', date('Y-m-d'))
                                          ->groupBy('attendence_type.id')
                                          ->get()
                                          ->getResultArray();
        
        return $stats;
    }

    /**
     * Get students with no attendance for a date
     */
    public function getAbsentStudents($classId, $sectionId, $date, $sessionId = null)
    {
        $builder = $this->db->table('student_session');
        $builder->select('student_session.*, students.firstname, students.lastname, students.admission_no')
                ->join('students', 'student_session.student_id = students.id')
                ->where('student_session.class_id', $classId)
                ->where('student_session.section_id', $sectionId)
                ->where('student_session.is_active', 'yes');

        if ($sessionId) {
            $builder->where('student_session.session_id', $sessionId);
        }

        // Exclude students who have attendance for this date
        $builder->whereNotIn('student_session.id', function($subquery) use ($date) {
            return $subquery->select('student_session_id')
                           ->from('student_attendences')
                           ->where('date', $date);
        });

        return $builder->get()->getResultArray();
    }

    /**
     * Get attendance types
     */
    public function getAttendanceTypes()
    {
        try {
            $types = $this->db->table('attendence_type')
                             ->where('is_active', 'yes')
                             ->get()
                             ->getResultArray();
            
            $dropdown = [];
            foreach ($types as $type) {
                $dropdown[$type['id']] = $type['type'];
            }
            
            return $dropdown;
        } catch (\Exception $e) {
            return [
                1 => 'Present',
                2 => 'Absent',
                3 => 'Late',
                4 => 'Half Day'
            ];
        }
    }

    /**
     * Get searchable columns for DataTables
     */
    protected function getSearchableColumns()
    {
        return $this->searchableColumns;
    }

    /**
     * Get orderable columns for DataTables
     */
    protected function getOrderableColumns()
    {
        return $this->orderableColumns;
    }

    /**
     * Sync attendance from biometric device data
     */
    public function syncFromBiometricData($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }

        // Load calendar helper
        helper('calendar');

        // Validate attendance date
        $validation = validate_attendance_date($date);
        if (!$validation['is_valid']) {
            return [
                'success' => false,
                'synced_count' => 0,
                'errors' => [],
                'message' => "Cannot sync attendance for {$date}: " . $validation['message']
            ];
        }

        $attLogModel = new \App\Models\AttLogModel();
        $biometricData = $attLogModel->getDailyAttendanceSummary($date);

        $syncedCount = 0;
        $errors = [];
        $warnings = [];

        // Add holiday warning if applicable
        if ($validation['is_holiday'] && !$validation['affects_attendance']) {
            $warnings[] = "Note: {$date} is a holiday ({$validation['holiday_info']['title']}) but attendance can still be marked.";
        }

        foreach ($biometricData as $record) {
            try {
                $studentId = $record['student_id'];
                if (!$studentId) continue;

                // Get student session
                $studentSessionModel = new \App\Models\StudentSessionModel();
                $studentSession = $studentSessionModel->where('student_id', $studentId)->first();

                if (!$studentSession) {
                    $errors[] = "No active session found for student ID: {$studentId}";
                    continue;
                }

                // Determine attendance type based on timing
                $attendanceType = $this->determineAttendanceType($record['first_scan'], $record['last_scan']);

                // Check if attendance already exists
                $existing = $this->where('student_session_id', $studentSession['id'])
                               ->where('date', $date)
                               ->first();

                $attendanceData = [
                    'student_session_id' => $studentSession['id'],
                    'date' => $date,
                    'attendence_type_id' => $attendanceType,
                    'biometric_attendence' => 1,
                    'biometric_device_data' => json_encode([
                        'first_scan' => $record['first_scan'],
                        'last_scan' => $record['last_scan'],
                        'total_scans' => $record['total_scans']
                    ]),
                    'remark' => $this->generateAttendanceRemark($date, $validation),
                    'user_agent' => 'Biometric Sync System'
                ];

                if ($existing) {
                    $this->update($existing['id'], $attendanceData);
                } else {
                    $this->insert($attendanceData);
                }

                $syncedCount++;

            } catch (\Exception $e) {
                $errors[] = "Error syncing student {$record['student_id']}: " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'synced_count' => $syncedCount,
            'errors' => $errors,
            'warnings' => $warnings,
            'message' => "Synced {$syncedCount} attendance records for {$date}"
        ];
    }

    /**
     * Determine attendance type based on scan times
     */
    private function determineAttendanceType($firstScan, $lastScan)
    {
        $schoolStart = strtotime(self::SCHOOL_START_TIME);
        $firstScanTime = strtotime(date('H:i:s', strtotime($firstScan)));

        // Calculate minutes late
        $minutesLate = ($firstScanTime - $schoolStart) / 60;

        if ($minutesLate <= 0) {
            return self::ATTENDANCE_TYPE_PRESENT; // On time
        } elseif ($minutesLate <= self::LATE_THRESHOLD_MINUTES) {
            return self::ATTENDANCE_TYPE_PRESENT; // Within acceptable range
        } else {
            return self::ATTENDANCE_TYPE_LATE; // Late arrival
        }
    }

    /**
     * Generate attendance remark based on date validation
     */
    private function generateAttendanceRemark($date, $validation)
    {
        $remark = 'Auto-synced from biometric device';

        if ($validation['is_holiday']) {
            $remark .= ' (Holiday: ' . $validation['holiday_info']['title'] . ')';
        }

        if ($validation['is_weekend']) {
            $remark .= ' (Weekend)';
        }

        return $remark;
    }

    /**
     * Get attendance with biometric data
     */
    public function getAttendanceWithBiometricData($filters = [])
    {
        $builder = $this->db->table($this->table);
        $builder->select('student_attendences.*, students.firstname, students.lastname, students.admission_no,
                         students.biometric_pin, classes.class, sections.section, attendence_type.type as attendance_type')
                ->join('student_session', 'student_attendences.student_session_id = student_session.id')
                ->join('students', 'student_session.student_id = students.id')
                ->join('classes', 'student_session.class_id = classes.id')
                ->join('sections', 'student_session.section_id = sections.id')
                ->join('attendence_type', 'student_attendences.attendence_type_id = attendence_type.id');

        // Apply filters
        if (!empty($filters['date'])) {
            $builder->where('student_attendences.date', $filters['date']);
        }

        if (!empty($filters['class_id'])) {
            $builder->where('student_session.class_id', $filters['class_id']);
        }

        if (!empty($filters['section_id'])) {
            $builder->where('student_session.section_id', $filters['section_id']);
        }

        if (!empty($filters['biometric_only'])) {
            $builder->where('student_attendences.biometric_attendence', 1);
        }

        return $builder->get()->getResultArray();
    }

    /**
     * Get attendance statistics with biometric data
     */
    public function getBiometricAttendanceStats($dateFrom, $dateTo, $classId = null, $sectionId = null)
    {
        $builder = $this->db->table($this->table);
        $builder->select('
            COUNT(*) as total_records,
            SUM(CASE WHEN biometric_attendence = 1 THEN 1 ELSE 0 END) as biometric_records,
            SUM(CASE WHEN attendence_type_id = 1 THEN 1 ELSE 0 END) as present_count,
            SUM(CASE WHEN attendence_type_id = 2 THEN 1 ELSE 0 END) as absent_count,
            SUM(CASE WHEN attendence_type_id = 3 THEN 1 ELSE 0 END) as late_count
        ')
        ->join('student_session', 'student_attendences.student_session_id = student_session.id')
        ->where('student_attendences.date >=', $dateFrom)
        ->where('student_attendences.date <=', $dateTo);

        if ($classId) {
            $builder->where('student_session.class_id', $classId);
        }

        if ($sectionId) {
            $builder->where('student_session.section_id', $sectionId);
        }

        return $builder->get()->getRowArray();
    }

    /**
     * Process class/activity attendance from biometric data
     */
    public function processClassAttendance($classId, $sectionId, $date, $timeFrom, $timeTo)
    {
        $attLogModel = new \App\Models\AttLogModel();

        // Get attendance logs within the specified time range
        $builder = $attLogModel->builder();
        $builder->select('att_log.*, students.id as student_id')
                ->join('students', 'att_log.pin = students.biometric_pin')
                ->join('student_session', 'students.id = student_session.student_id')
                ->where('student_session.class_id', $classId)
                ->where('student_session.section_id', $sectionId)
                ->where('DATE(att_log.scan_date)', $date)
                ->where('TIME(att_log.scan_date) >=', $timeFrom)
                ->where('TIME(att_log.scan_date) <=', $timeTo);

        $classAttendance = $builder->get()->getResultArray();

        // Process into subject attendance
        $subjectAttendanceModel = new \App\Models\StudentSubjectAttendanceModel();
        $processedCount = 0;

        foreach ($classAttendance as $record) {
            $studentSessionModel = new \App\Models\StudentSessionModel();
            $studentSession = $studentSessionModel->where('student_id', $record['student_id'])->first();

            if ($studentSession) {
                $attendanceData = [
                    'student_session_id' => $studentSession['id'],
                    'date' => $date,
                    'attendence_type' => 'Present',
                    'remark' => "Auto-detected from biometric scan at " . date('H:i:s', strtotime($record['scan_date']))
                ];

                // Check if already exists
                $existing = $subjectAttendanceModel
                    ->where('student_session_id', $studentSession['id'])
                    ->where('date', $date)
                    ->first();

                if (!$existing) {
                    $subjectAttendanceModel->insert($attendanceData);
                    $processedCount++;
                }
            }
        }

        return [
            'success' => true,
            'processed_count' => $processedCount,
            'message' => "Processed {$processedCount} class attendance records"
        ];
    }

    /**
     * Validate attendance date before creating/updating
     */
    public function validateAttendanceDate($date, $sessionId = null)
    {
        helper('calendar');
        return validate_attendance_date($date, $sessionId);
    }

    /**
     * Create attendance with holiday validation
     */
    public function createAttendanceWithValidation($data)
    {
        // Validate attendance date
        $validation = $this->validateAttendanceDate($data['date']);

        if (!$validation['is_valid']) {
            return [
                'success' => false,
                'message' => $validation['message'],
                'validation' => $validation
            ];
        }

        // Add holiday information to remark if applicable
        if ($validation['is_holiday']) {
            $holidayRemark = ' (Holiday: ' . $validation['holiday_info']['title'] . ')';
            $data['remark'] = ($data['remark'] ?? '') . $holidayRemark;
        }

        // Create attendance record
        $result = $this->insert($data);

        if ($result) {
            return [
                'success' => true,
                'message' => 'Attendance created successfully',
                'data' => $this->find($this->getInsertID()),
                'validation' => $validation
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to create attendance record',
            'errors' => $this->errors()
        ];
    }

    /**
     * Get attendance statistics with holiday consideration
     */
    public function getAttendanceStatsWithHolidays($dateFrom, $dateTo, $classId = null, $sectionId = null, $sessionId = null)
    {
        helper(['calendar', 'session']);

        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        // Get working days in the period
        $workingDays = get_working_days($dateFrom, $dateTo, $sessionId);
        $totalWorkingDays = count($workingDays);

        // Get regular attendance stats
        $stats = $this->getBiometricAttendanceStats($dateFrom, $dateTo, $classId, $sectionId);

        // Add holiday information
        $holidays = get_holiday_conflicts($dateFrom, $dateTo, $sessionId);

        $stats['total_working_days'] = $totalWorkingDays;
        $stats['total_holidays'] = count($holidays);
        $stats['attendance_rate'] = $totalWorkingDays > 0 ?
            round(($stats['present_count'] / $totalWorkingDays) * 100, 2) : 0;

        return $stats;
    }
}
