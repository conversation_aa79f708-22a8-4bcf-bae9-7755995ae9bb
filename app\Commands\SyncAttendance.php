<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\AttLogModel;
use App\Models\StudentAttendanceModel;

class SyncAttendance extends BaseCommand
{
    protected $group = 'Student Apps';
    protected $name = 'attendance:sync';
    protected $description = 'Sync student attendance from biometric devices';

    protected $usage = 'attendance:sync [date]';
    protected $arguments = [
        'date' => 'Date to sync (YYYY-MM-DD format). Defaults to today.'
    ];

    protected $options = [
        '--all' => 'Sync all unprocessed records',
        '--device' => 'Sync from specific device serial number',
        '--link-pins' => 'Attempt to link unlinked PINs to students'
    ];

    public function run(array $params)
    {
        CLI::write('Student Attendance Sync Tool', 'yellow');
        CLI::write('================================', 'yellow');

        $date = $params[0] ?? date('Y-m-d');
        $syncAll = CLI::getOption('all');
        $deviceSerial = CLI::getOption('device');
        $linkPins = CLI::getOption('link-pins');

        try {
            $attLogModel = new AttLogModel();
            $attendanceModel = new StudentAttendanceModel();

            // Link unlinked PINs if requested
            if ($linkPins) {
                $this->linkUnlinkedPins($attLogModel);
            }

            // Sync attendance data
            if ($syncAll) {
                $this->syncAllUnprocessed($attLogModel, $attendanceModel);
            } else {
                $this->syncByDate($attendanceModel, $date);
            }

            // Show device statistics
            $this->showDeviceStats($attLogModel);

        } catch (\Exception $e) {
            CLI::error('Error during sync: ' . $e->getMessage());
            return EXIT_ERROR;
        }

        CLI::write('Sync completed successfully!', 'green');
        return EXIT_SUCCESS;
    }

    /**
     * Sync attendance for specific date
     */
    private function syncByDate($attendanceModel, $date)
    {
        CLI::write("Syncing attendance for date: {$date}", 'cyan');
        
        $result = $attendanceModel->syncFromBiometricData($date);
        
        if ($result['success']) {
            CLI::write("✓ Synced {$result['synced_count']} records", 'green');
            
            if (!empty($result['errors'])) {
                CLI::write('Errors encountered:', 'red');
                foreach ($result['errors'] as $error) {
                    CLI::write("  - {$error}", 'red');
                }
            }
        } else {
            CLI::error('Sync failed: ' . $result['message']);
        }
    }

    /**
     * Sync all unprocessed records
     */
    private function syncAllUnprocessed($attLogModel, $attendanceModel)
    {
        CLI::write('Syncing all unprocessed records...', 'cyan');
        
        // Get date range of unprocessed records
        $builder = $attLogModel->builder();
        $builder->select('DATE(scan_date) as date')
                ->where('student_id IS NOT NULL')
                ->groupBy('DATE(scan_date)')
                ->orderBy('date', 'ASC');
        
        $dates = $builder->get()->getResultArray();
        
        $totalSynced = 0;
        $allErrors = [];
        
        foreach ($dates as $dateRecord) {
            $date = $dateRecord['date'];
            CLI::write("Processing date: {$date}");
            
            $result = $attendanceModel->syncFromBiometricData($date);
            
            if ($result['success']) {
                $totalSynced += $result['synced_count'];
                CLI::write("  ✓ Synced {$result['synced_count']} records", 'green');
                
                if (!empty($result['errors'])) {
                    $allErrors = array_merge($allErrors, $result['errors']);
                }
            } else {
                CLI::write("  ✗ Failed: {$result['message']}", 'red');
            }
        }
        
        CLI::write("Total synced: {$totalSynced} records", 'green');
        
        if (!empty($allErrors)) {
            CLI::write('Total errors: ' . count($allErrors), 'red');
        }
    }

    /**
     * Attempt to link unlinked PINs to students
     */
    private function linkUnlinkedPins($attLogModel)
    {
        CLI::write('Attempting to link unlinked PINs...', 'cyan');
        
        $unlinkedRecords = $attLogModel->getUnlinkedRecords(50);
        $linkedCount = 0;
        
        foreach ($unlinkedRecords as $record) {
            // Try to find student by admission number or roll number matching PIN
            $studentsModel = new \App\Models\StudentsModel();
            
            $student = $studentsModel->where('admission_no', $record['pin'])
                                   ->orWhere('roll_no', $record['pin'])
                                   ->first();
            
            if ($student) {
                $result = $attLogModel->linkPinToStudent($record['pin'], $student['id']);
                if ($result) {
                    $linkedCount++;
                    CLI::write("  ✓ Linked PIN {$record['pin']} to student {$student['firstname']} {$student['lastname']}", 'green');
                }
            }
        }
        
        CLI::write("Linked {$linkedCount} PINs to students", 'green');
    }

    /**
     * Show device statistics
     */
    private function showDeviceStats($attLogModel)
    {
        CLI::write('Device Statistics:', 'yellow');
        CLI::write('==================', 'yellow');
        
        $stats = $attLogModel->getDeviceStats();
        
        if (empty($stats)) {
            CLI::write('No device data found.', 'red');
            return;
        }
        
        foreach ($stats as $device) {
            CLI::write("Device: {$device['device_serial']}", 'cyan');
            CLI::write("  Total Records: {$device['total_records']}");
            CLI::write("  First Scan: {$device['first_scan']}");
            CLI::write("  Last Scan: {$device['last_scan']}");
            CLI::write('');
        }
    }
}
