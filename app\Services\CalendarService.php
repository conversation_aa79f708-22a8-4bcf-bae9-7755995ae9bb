<?php

namespace App\Services;

use App\Models\AcademicCalendarModel;
use App\Models\HolidayTypesModel;

class CalendarService
{
    protected $calendarModel;
    protected $holidayTypesModel;
    protected static $instance;

    public function __construct()
    {
        $this->calendarModel = new AcademicCalendarModel();
        $this->holidayTypesModel = new HolidayTypesModel();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Check if a date is a school holiday
     */
    public function isSchoolHoliday($date, $sessionId = null, $appliesTo = 'students')
    {
        return $this->calendarModel->isHoliday($date, $sessionId, $appliesTo);
    }

    /**
     * Check if a date affects attendance
     */
    public function affectsAttendance($date, $sessionId = null)
    {
        helper('session');
        
        if (!$sessionId) {
            $sessionId = get_current_session_id();
        }

        $holidays = $this->calendarModel->getAttendanceAffectingHolidays($date, $date, $sessionId);
        return !empty($holidays);
    }

    /**
     * Get working days between two dates
     */
    public function getWorkingDays($startDate, $endDate, $sessionId = null, $excludeWeekends = true)
    {
        $workingDays = [];
        $currentDate = strtotime($startDate);
        $endTimestamp = strtotime($endDate);

        while ($currentDate <= $endTimestamp) {
            $dateString = date('Y-m-d', $currentDate);
            $dayOfWeek = date('w', $currentDate); // 0 = Sunday, 6 = Saturday

            // Skip weekends if requested
            if ($excludeWeekends && ($dayOfWeek == 0 || $dayOfWeek == 6)) {
                $currentDate = strtotime('+1 day', $currentDate);
                continue;
            }

            // Skip holidays that affect attendance
            if (!$this->affectsAttendance($dateString, $sessionId)) {
                $workingDays[] = $dateString;
            }

            $currentDate = strtotime('+1 day', $currentDate);
        }

        return $workingDays;
    }

    /**
     * Get holiday information for a specific date
     */
    public function getHolidayInfo($date, $sessionId = null)
    {
        $holidays = $this->calendarModel->getHolidaysInRange($date, $date, $sessionId);
        return !empty($holidays) ? $holidays[0] : null;
    }

    /**
     * Get upcoming holidays
     */
    public function getUpcomingHolidays($limit = 5, $sessionId = null)
    {
        return $this->calendarModel->getUpcomingHolidays($limit, $sessionId);
    }

    /**
     * Get holidays for calendar display
     */
    public function getCalendarData($year, $month, $sessionId = null)
    {
        $startDate = date('Y-m-01', strtotime("$year-$month-01"));
        $endDate = date('Y-m-t', strtotime("$year-$month-01"));

        $holidays = $this->calendarModel->getCalendarEvents([
            'session_id' => $sessionId,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);

        $calendarData = [];
        foreach ($holidays as $holiday) {
            $calendarData[] = [
                'id' => $holiday['id'],
                'title' => $holiday['title'],
                'start' => $holiday['start_date'],
                'end' => date('Y-m-d', strtotime($holiday['end_date'] . ' +1 day')), // FullCalendar end is exclusive
                'color' => $holiday['color'],
                'description' => $holiday['description'],
                'holiday_type' => $holiday['holiday_type_name'],
                'affects_attendance' => $holiday['affects_attendance'],
                'is_academic_break' => $holiday['is_academic_break'],
                'applies_to' => $holiday['applies_to']
            ];
        }

        return $calendarData;
    }

    /**
     * Validate attendance date
     */
    public function validateAttendanceDate($date, $sessionId = null)
    {
        $validation = [
            'is_valid' => true,
            'is_holiday' => false,
            'is_weekend' => false,
            'affects_attendance' => false,
            'message' => '',
            'holiday_info' => null
        ];

        // Check if weekend
        $dayOfWeek = date('w', strtotime($date));
        if ($dayOfWeek == 0 || $dayOfWeek == 6) {
            $validation['is_weekend'] = true;
            $validation['message'] = 'This is a weekend day';
        }

        // Check if holiday
        if ($this->isSchoolHoliday($date, $sessionId)) {
            $validation['is_holiday'] = true;
            $validation['holiday_info'] = $this->getHolidayInfo($date, $sessionId);
            
            if ($this->affectsAttendance($date, $sessionId)) {
                $validation['affects_attendance'] = true;
                $validation['is_valid'] = false;
                $validation['message'] = 'Attendance cannot be marked on this holiday: ' . $validation['holiday_info']['title'];
            } else {
                $validation['message'] = 'This is a holiday but attendance can still be marked: ' . $validation['holiday_info']['title'];
            }
        }

        return $validation;
    }

    /**
     * Get academic calendar summary
     */
    public function getCalendarSummary($sessionId = null)
    {
        $stats = $this->calendarModel->getStatistics($sessionId);
        $upcomingHolidays = $this->getUpcomingHolidays(3, $sessionId);

        return [
            'statistics' => $stats,
            'upcoming_holidays' => $upcomingHolidays,
            'next_holiday' => !empty($upcomingHolidays) ? $upcomingHolidays[0] : null
        ];
    }

    /**
     * Generate default academic calendar for a session
     */
    public function generateDefaultCalendar($sessionId, $academicYear)
    {
        // Extract year from academic year (e.g., "2024-2025" -> 2024)
        $startYear = (int)substr($academicYear, 0, 4);
        $endYear = $startYear + 1;

        $defaultHolidays = [
            [
                'title' => 'Summer Vacation',
                'start_date' => $startYear . '-06-01',
                'end_date' => $startYear . '-06-30',
                'holiday_type' => 'Academic Break',
                'description' => 'Annual summer vacation'
            ],
            [
                'title' => 'Independence Day',
                'start_date' => $startYear . '-08-17',
                'end_date' => $startYear . '-08-17',
                'holiday_type' => 'National Holiday',
                'description' => 'National Independence Day'
            ],
            [
                'title' => 'Winter Break',
                'start_date' => $startYear . '-12-20',
                'end_date' => $endYear . '-01-05',
                'holiday_type' => 'Academic Break',
                'description' => 'Winter vacation and New Year break'
            ],
            [
                'title' => 'Mid-term Break',
                'start_date' => $endYear . '-03-15',
                'end_date' => $endYear . '-03-22',
                'holiday_type' => 'Academic Break',
                'description' => 'Mid-semester break'
            ]
        ];

        $createdCount = 0;
        $errors = [];

        foreach ($defaultHolidays as $holiday) {
            try {
                // Find holiday type
                $holidayType = $this->holidayTypesModel->where('name', $holiday['holiday_type'])->first();
                
                if ($holidayType) {
                    $holidayData = [
                        'session_id' => $sessionId,
                        'holiday_type_id' => $holidayType['id'],
                        'title' => $holiday['title'],
                        'description' => $holiday['description'],
                        'start_date' => $holiday['start_date'],
                        'end_date' => $holiday['end_date'],
                        'is_recurring' => 1,
                        'recurrence_pattern' => 'yearly',
                        'applies_to' => 'all',
                        'is_active' => 'yes'
                    ];

                    $result = $this->calendarModel->createHoliday($holidayData);
                    if ($result['success']) {
                        $createdCount++;
                    } else {
                        $errors[] = $result['message'];
                    }
                }
            } catch (\Exception $e) {
                $errors[] = "Error creating holiday '{$holiday['title']}': " . $e->getMessage();
            }
        }

        return [
            'success' => true,
            'created_count' => $createdCount,
            'errors' => $errors,
            'message' => "Generated {$createdCount} default holidays for academic year {$academicYear}"
        ];
    }

    /**
     * Get attendance-safe dates in a range
     */
    public function getAttendanceSafeDates($startDate, $endDate, $sessionId = null)
    {
        $safeDates = [];
        $currentDate = strtotime($startDate);
        $endTimestamp = strtotime($endDate);

        while ($currentDate <= $endTimestamp) {
            $dateString = date('Y-m-d', $currentDate);
            
            $validation = $this->validateAttendanceDate($dateString, $sessionId);
            if ($validation['is_valid']) {
                $safeDates[] = $dateString;
            }

            $currentDate = strtotime('+1 day', $currentDate);
        }

        return $safeDates;
    }

    /**
     * Check if current time is within school hours
     */
    public function isSchoolHours($time = null)
    {
        if (!$time) {
            $time = date('H:i:s');
        }

        // Default school hours (can be made configurable)
        $schoolStart = '07:00:00';
        $schoolEnd = '15:00:00';

        return $time >= $schoolStart && $time <= $schoolEnd;
    }

    /**
     * Get holiday conflicts for a date range
     */
    public function getHolidayConflicts($startDate, $endDate, $sessionId = null)
    {
        $holidays = $this->calendarModel->getHolidaysInRange($startDate, $endDate, $sessionId);
        
        $conflicts = [];
        foreach ($holidays as $holiday) {
            if ($holiday['affects_attendance']) {
                $conflicts[] = [
                    'date' => $holiday['start_date'],
                    'end_date' => $holiday['end_date'],
                    'title' => $holiday['title'],
                    'type' => $holiday['holiday_type_name'],
                    'message' => "Holiday '{$holiday['title']}' affects attendance"
                ];
            }
        }

        return $conflicts;
    }
}
