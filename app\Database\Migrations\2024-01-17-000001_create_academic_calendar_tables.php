<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateAcademicCalendarTables extends Migration
{
    public function up()
    {
        // Create holiday_types table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'Holiday type name'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Holiday type description'
            ],
            'color' => [
                'type' => 'VARCHAR',
                'constraint' => 7,
                'null' => false,
                'default' => '#007bff',
                'comment' => 'Display color for calendar'
            ],
            'is_academic_break' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '1 if this affects academic activities'
            ],
            'affects_attendance' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 1,
                'comment' => '1 if this affects attendance marking'
            ],
            'is_active' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
                'default' => 'yes'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('is_active');
        $this->forge->createTable('holiday_types');

        // Create academic_calendar table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'session_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
                'comment' => 'Academic session'
            ],
            'holiday_type_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => false,
                'comment' => 'Holiday/Event title'
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Detailed description'
            ],
            'start_date' => [
                'type' => 'DATE',
                'null' => false,
                'comment' => 'Holiday start date'
            ],
            'end_date' => [
                'type' => 'DATE',
                'null' => false,
                'comment' => 'Holiday end date'
            ],
            'is_recurring' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '1 if holiday recurs annually'
            ],
            'recurrence_pattern' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'comment' => 'yearly, monthly, weekly, etc.'
            ],
            'applies_to' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => false,
                'default' => 'all',
                'comment' => 'all, students, staff, specific_classes'
            ],
            'class_ids' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'JSON array of class IDs if applies_to=specific_classes'
            ],
            'notification_sent' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => false,
                'default' => 0,
                'comment' => '1 if notification has been sent'
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'User who created this entry'
            ],
            'is_active' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
                'default' => 'yes'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addPrimaryKey('id');
        $this->forge->addKey('session_id');
        $this->forge->addKey('holiday_type_id');
        $this->forge->addKey('start_date');
        $this->forge->addKey('end_date');
        $this->forge->addKey('is_active');
        
        $this->forge->addForeignKey('session_id', 'sessions', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('holiday_type_id', 'holiday_types', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('academic_calendar');

        // Insert default holiday types
        $this->insertDefaultHolidayTypes();
    }

    public function down()
    {
        $this->forge->dropTable('academic_calendar');
        $this->forge->dropTable('holiday_types');
    }

    /**
     * Insert default holiday types
     */
    private function insertDefaultHolidayTypes()
    {
        $db = \Config\Database::connect();
        
        $holidayTypes = [
            [
                'name' => 'National Holiday',
                'description' => 'National public holidays',
                'color' => '#dc3545',
                'is_academic_break' => 1,
                'affects_attendance' => 1,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Religious Holiday',
                'description' => 'Religious observances and festivals',
                'color' => '#28a745',
                'is_academic_break' => 1,
                'affects_attendance' => 1,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Academic Break',
                'description' => 'Semester breaks, summer vacation, etc.',
                'color' => '#007bff',
                'is_academic_break' => 1,
                'affects_attendance' => 1,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Examination Period',
                'description' => 'Exam periods and assessment days',
                'color' => '#ffc107',
                'is_academic_break' => 0,
                'affects_attendance' => 0,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'School Event',
                'description' => 'Sports day, cultural events, etc.',
                'color' => '#17a2b8',
                'is_academic_break' => 0,
                'affects_attendance' => 0,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Staff Development',
                'description' => 'Teacher training and development days',
                'color' => '#6f42c1',
                'is_academic_break' => 1,
                'affects_attendance' => 1,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'Emergency Closure',
                'description' => 'Weather, emergency, or unexpected closures',
                'color' => '#fd7e14',
                'is_academic_break' => 1,
                'affects_attendance' => 1,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $db->table('holiday_types')->insertBatch($holidayTypes);
    }
}
