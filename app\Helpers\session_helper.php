<?php

use App\Services\SessionContextService;

if (!function_exists('get_current_session')) {
    /**
     * Get current active session
     */
    function get_current_session()
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->getCurrentSession();
    }
}

if (!function_exists('get_current_session_id')) {
    /**
     * Get current session ID
     */
    function get_current_session_id()
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->getCurrentSessionId();
    }
}

if (!function_exists('get_current_academic_year')) {
    /**
     * Get current academic year
     */
    function get_current_academic_year()
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->getCurrentAcademicYear();
    }
}

if (!function_exists('get_session_filter')) {
    /**
     * Get session filter data for views
     */
    function get_session_filter()
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->getSessionFilter();
    }
}

if (!function_exists('apply_session_filter')) {
    /**
     * Apply session filter to query builder
     */
    function apply_session_filter($builder, $sessionField = 'session_id', $sessionId = null)
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->applySessionFilter($builder, $sessionField, $sessionId);
    }
}

if (!function_exists('is_session_active')) {
    /**
     * Check if session is active
     */
    function is_session_active($sessionId = null)
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->isSessionActive($sessionId);
    }
}

if (!function_exists('switch_session_context')) {
    /**
     * Switch session context
     */
    function switch_session_context($sessionId)
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->switchSession($sessionId);
    }
}

if (!function_exists('get_session_breadcrumb')) {
    /**
     * Get session breadcrumb info
     */
    function get_session_breadcrumb()
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->getSessionBreadcrumb();
    }
}

if (!function_exists('validate_session_ownership')) {
    /**
     * Validate that data belongs to current session
     */
    function validate_session_ownership($data, $sessionField = 'session_id')
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->validateSessionOwnership($data, $sessionField);
    }
}

if (!function_exists('add_session_context')) {
    /**
     * Add session context to data
     */
    function add_session_context($data, $sessionField = 'session_id')
    {
        $sessionService = SessionContextService::getInstance();
        return $sessionService->addSessionContext($data, $sessionField);
    }
}

if (!function_exists('format_academic_year')) {
    /**
     * Format academic year for display
     */
    function format_academic_year($academicYear)
    {
        if (empty($academicYear)) {
            return 'N/A';
        }
        
        // If already formatted (e.g., "2024-2025"), return as is
        if (strpos($academicYear, '-') !== false) {
            return $academicYear;
        }
        
        // If single year (e.g., "2024"), format as "2024-2025"
        if (is_numeric($academicYear) && strlen($academicYear) == 4) {
            return $academicYear . '-' . ($academicYear + 1);
        }
        
        return $academicYear;
    }
}

if (!function_exists('get_session_dropdown')) {
    /**
     * Get sessions dropdown for forms
     */
    function get_session_dropdown($includeInactive = false)
    {
        $sessionsModel = new \App\Models\SessionsModel();
        
        if ($includeInactive) {
            return $sessionsModel->getAllForDropdown();
        } else {
            return $sessionsModel->getForDropdown();
        }
    }
}

if (!function_exists('is_current_session')) {
    /**
     * Check if given session ID is the current session
     */
    function is_current_session($sessionId)
    {
        $currentSessionId = get_current_session_id();
        return $currentSessionId && $currentSessionId == $sessionId;
    }
}

if (!function_exists('get_session_date_range')) {
    /**
     * Get session date range
     */
    function get_session_date_range($sessionId = null)
    {
        if (!$sessionId) {
            $session = get_current_session();
        } else {
            $sessionsModel = new \App\Models\SessionsModel();
            $session = $sessionsModel->find($sessionId);
        }
        
        if (!$session) {
            return null;
        }
        
        return [
            'start_date' => $session['start_date'],
            'end_date' => $session['end_date'],
            'formatted_range' => format_date_range($session['start_date'], $session['end_date'])
        ];
    }
}

if (!function_exists('format_date_range')) {
    /**
     * Format date range for display
     */
    function format_date_range($startDate, $endDate)
    {
        if (!$startDate || !$endDate) {
            return 'N/A';
        }
        
        $start = date('M d, Y', strtotime($startDate));
        $end = date('M d, Y', strtotime($endDate));
        
        return $start . ' - ' . $end;
    }
}

if (!function_exists('get_session_status_badge')) {
    /**
     * Get session status badge HTML
     */
    function get_session_status_badge($session)
    {
        if (!$session) {
            return '<span class="badge badge-secondary">No Session</span>';
        }
        
        $badges = [];
        
        // Active status
        if ($session['is_active'] === 'yes') {
            $badges[] = '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Active</span>';
        } else {
            $badges[] = '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Inactive</span>';
        }
        
        // Current status
        if ($session['is_current'] === 'yes') {
            $badges[] = '<span class="inline-flex rounded-full bg-primary bg-opacity-10 py-1 px-3 text-sm font-medium text-primary">Current</span>';
        }
        
        return implode(' ', $badges);
    }
}
