<?php

namespace App\Controllers;

use App\Models\StudentAttendanceModel;
use App\Models\StudentSessionModel;
use App\Models\StudentsModel;
use App\Models\ClassesModel;
use App\Models\SectionsModel;
use App\Models\AttLogModel;

class StudentAttendanceController extends BaseCrudController
{
    protected $studentSessionModel;
    protected $studentsModel;
    protected $classesModel;
    protected $sectionsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentAttendanceModel();
        $this->studentSessionModel = new StudentSessionModel();
        $this->studentsModel = new StudentsModel();
        $this->classesModel = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        
        $this->viewPath = 'admin/student_apps/attendance';
        $this->routePrefix = 'admin/student-apps/attendance';
        $this->entityName = 'Student Attendance';
        $this->entityNamePlural = 'Student Attendance';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'student_sessions' => $this->getStudentSessionsForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'attendance_types' => $this->model->getAttendanceTypes()
        ];
    }

    /**
     * Get student sessions for dropdown
     */
    private function getStudentSessionsForDropdown()
    {
        $sessions = $this->studentSessionModel->getStudentSessionWithDetails();
        $dropdown = [];
        
        foreach ($sessions as $session) {
            $label = $session['firstname'] . ' ' . $session['lastname'] . ' (' . $session['admission_no'] . ') - ' . $session['class'] . ' ' . $session['section'];
            $dropdown[$session['id']] = $label;
        }
        
        return $dropdown;
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters including session context
        $filters = [
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id'),
            'date' => $this->request->getPost('date'),
            'attendance_type' => $this->request->getPost('attendance_type'),
            'source' => $this->request->getPost('source'),
            'session_id' => session()->get('current_session_id')
        ];

        // Get all data first
        $allData = $this->model->getAttendanceWithDetails($filters);
        $totalRecords = count($allData);

        // Apply search if provided
        if (!empty($searchValue)) {
            $allData = array_filter($allData, function($record) use ($searchValue) {
                return stripos($record['firstname'] . ' ' . $record['lastname'], $searchValue) !== false ||
                       stripos($record['admission_no'], $searchValue) !== false ||
                       stripos($record['class'], $searchValue) !== false ||
                       stripos($record['section'], $searchValue) !== false ||
                       stripos($record['session_name'], $searchValue) !== false;
            });
        }

        $filteredRecords = count($allData);

        // Apply ordering
        $columns = ['id', 'firstname', 'date', 'attendance_type'];
        if (isset($columns[$orderColumn])) {
            $sortField = $columns[$orderColumn];
            usort($allData, function($a, $b) use ($sortField, $orderDir) {
                if ($orderDir === 'desc') {
                    return $b[$sortField] <=> $a[$sortField];
                }
                return $a[$sortField] <=> $b[$sortField];
            });
        }

        // Apply pagination
        $records = array_slice($allData, $start, $length);

        $data = [];
        foreach ($records as $record) {
            $attendanceType = $this->getAttendanceTypeBadge($record['attendance_type']);
            $actions = $this->getActionButtons($record);

            $data[] = [
                'id' => $record['id'],
                'student_name' => $record['firstname'] . ' ' . $record['lastname'],
                'admission_no' => $record['admission_no'],
                'class_section' => $record['class'] . ' - ' . $record['section'],
                'session' => $record['session_name'] ?? 'N/A',
                'academic_year' => $record['academic_year'] ?? 'N/A',
                'date' => date('M j, Y', strtotime($record['date'])),
                'attendance_type' => $attendanceType,
                'source' => isset($record['biometric_attendence']) && $record['biometric_attendence'] ?
                           '<span class="text-success"><i class="fas fa-fingerprint"></i> Biometric</span>' :
                           '<span class="text-primary"><i class="fas fa-user"></i> Manual</span>',
                'remark' => $record['remark'] ?? '-',
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Mark attendance for multiple students
     */
    public function markAttendance()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $data = $this->request->getPost();
        $result = $this->model->markBulkAttendance($data);

        return $this->response->setJSON($result);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Sync attendance from biometric devices
     */
    public function syncBiometric()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $date = $this->request->getPost('date') ?: date('Y-m-d');

        try {
            $result = $this->model->syncFromBiometricData($date);

            // Add holiday warnings to the response
            if (!empty($result['warnings'])) {
                $result['message'] .= ' ' . implode(' ', $result['warnings']);
            }

            return $this->response->setJSON($result);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get biometric attendance data
     */
    public function getBiometricData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $filters = [
            'date' => $this->request->getGet('date'),
            'class_id' => $this->request->getGet('class_id'),
            'section_id' => $this->request->getGet('section_id'),
            'biometric_only' => $this->request->getGet('biometric_only')
        ];

        $data = $this->model->getAttendanceWithBiometricData($filters);

        return $this->response->setJSON([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get device statistics
     */
    public function getDeviceStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $attLogModel = new AttLogModel();
        $stats = $attLogModel->getDeviceStats();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Link PIN to student
     */
    public function linkPin()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $pin = $this->request->getPost('pin');
        $studentId = $this->request->getPost('student_id');

        if (!$pin || !$studentId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'PIN and Student ID are required'
            ]);
        }

        try {
            $attLogModel = new AttLogModel();
            $result = $attLogModel->linkPinToStudent($pin, $studentId);

            if ($result) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'PIN linked successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to link PIN'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error linking PIN: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get unlinked attendance records
     */
    public function getUnlinkedRecords()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $attLogModel = new AttLogModel();
        $records = $attLogModel->getUnlinkedRecords(50);

        return $this->response->setJSON([
            'success' => true,
            'data' => $records
        ]);
    }

    /**
     * Get attendance type badge HTML
     */
    private function getAttendanceTypeBadge($type)
    {
        switch ($type) {
            case 'Present':
                return '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Present</span>';
            case 'Late':
                return '<span class="inline-flex rounded-full bg-warning bg-opacity-10 py-1 px-3 text-sm font-medium text-warning">Late</span>';
            case 'Absent':
                return '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Absent</span>';
            case 'Half Day':
                return '<span class="inline-flex rounded-full bg-info bg-opacity-10 py-1 px-3 text-sm font-medium text-info">Half Day</span>';
            default:
                return '<span class="inline-flex rounded-full bg-gray bg-opacity-10 py-1 px-3 text-sm font-medium text-gray">Unknown</span>';
        }
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                        <i class="fas fa-edit"></i>
                    </a>';

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }

    /**
     * Validate attendance date for holidays
     */
    public function validateDate()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $date = $this->request->getPost('date');
        $sessionId = $this->request->getPost('session_id') ?: session()->get('current_session_id');

        if (!$date) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Date is required'
            ]);
        }

        helper('calendar');
        $validation = validate_attendance_date($date, $sessionId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $validation
        ]);
    }

    /**
     * Get attendance statistics with holiday consideration
     */
    public function getStatsWithHolidays()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $dateFrom = $this->request->getGet('date_from') ?: date('Y-m-01');
        $dateTo = $this->request->getGet('date_to') ?: date('Y-m-t');
        $classId = $this->request->getGet('class_id');
        $sectionId = $this->request->getGet('section_id');
        $sessionId = $this->request->getGet('session_id') ?: session()->get('current_session_id');

        $stats = $this->model->getAttendanceStatsWithHolidays($dateFrom, $dateTo, $classId, $sectionId, $sessionId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }
}
