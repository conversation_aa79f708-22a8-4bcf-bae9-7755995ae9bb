<?php

namespace App\Services;

use App\Models\SessionsModel;

class SessionContextService
{
    protected $sessionsModel;
    protected $currentSession;
    protected static $instance;

    public function __construct()
    {
        $this->sessionsModel = new SessionsModel();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Get current active session
     */
    public function getCurrentSession()
    {
        if ($this->currentSession === null) {
            $this->currentSession = $this->sessionsModel->getCurrentSession();
        }
        return $this->currentSession;
    }

    /**
     * Get current session ID
     */
    public function getCurrentSessionId()
    {
        $session = $this->getCurrentSession();
        return $session ? $session['id'] : null;
    }

    /**
     * Get current academic year
     */
    public function getCurrentAcademicYear()
    {
        $session = $this->getCurrentSession();
        return $session ? $session['academic_year'] : null;
    }

    /**
     * Set current session (for switching context)
     */
    public function setCurrentSession($sessionId)
    {
        $this->currentSession = $this->sessionsModel->find($sessionId);
        
        // Store in session for persistence
        session()->set('current_session_id', $sessionId);
        
        return $this->currentSession;
    }

    /**
     * Get session from user session or database
     */
    public function getContextSession()
    {
        // Check if user has selected a specific session
        $sessionId = session()->get('current_session_id');
        
        if ($sessionId) {
            $session = $this->sessionsModel->find($sessionId);
            if ($session && $session['is_active'] === 'yes') {
                return $session;
            }
        }
        
        // Fall back to current session
        return $this->getCurrentSession();
    }

    /**
     * Get all active sessions for dropdown
     */
    public function getActiveSessionsDropdown()
    {
        return $this->sessionsModel->getForDropdown();
    }

    /**
     * Check if session is valid and active
     */
    public function isValidSession($sessionId)
    {
        if (!$sessionId) {
            return false;
        }
        
        $session = $this->sessionsModel->find($sessionId);
        return $session && $session['is_active'] === 'yes';
    }

    /**
     * Get session statistics
     */
    public function getSessionStats($sessionId = null)
    {
        if (!$sessionId) {
            $sessionId = $this->getCurrentSessionId();
        }
        
        return $this->sessionsModel->getSessionStatistics($sessionId);
    }

    /**
     * Apply session filter to query builder
     */
    public function applySessionFilter($builder, $sessionField = 'session_id', $sessionId = null)
    {
        if (!$sessionId) {
            $sessionId = $this->getCurrentSessionId();
        }
        
        if ($sessionId) {
            $builder->where($sessionField, $sessionId);
        }
        
        return $builder;
    }

    /**
     * Get session-aware data with automatic filtering
     */
    public function getSessionAwareData($model, $sessionField = 'session_id', $sessionId = null)
    {
        if (!$sessionId) {
            $sessionId = $this->getCurrentSessionId();
        }
        
        if ($sessionId) {
            return $model->where($sessionField, $sessionId)->findAll();
        }
        
        return $model->findAll();
    }

    /**
     * Validate session dates
     */
    public function isSessionActive($sessionId = null)
    {
        if (!$sessionId) {
            $sessionId = $this->getCurrentSessionId();
        }
        
        $session = $this->sessionsModel->find($sessionId);
        
        if (!$session || $session['is_active'] !== 'yes') {
            return false;
        }
        
        // Check date range if available
        if ($session['start_date'] && $session['end_date']) {
            $now = date('Y-m-d');
            return $now >= $session['start_date'] && $now <= $session['end_date'];
        }
        
        return true;
    }

    /**
     * Get session breadcrumb info
     */
    public function getSessionBreadcrumb()
    {
        $session = $this->getContextSession();
        
        if ($session) {
            return [
                'name' => $session['session'],
                'academic_year' => $session['academic_year'],
                'is_current' => $session['is_current'] === 'yes'
            ];
        }
        
        return null;
    }

    /**
     * Switch session context
     */
    public function switchSession($sessionId)
    {
        if (!$this->isValidSession($sessionId)) {
            return [
                'success' => false,
                'message' => 'Invalid or inactive session'
            ];
        }
        
        $this->setCurrentSession($sessionId);
        
        return [
            'success' => true,
            'message' => 'Session context switched successfully',
            'session' => $this->currentSession
        ];
    }

    /**
     * Clear session context (revert to default)
     */
    public function clearSessionContext()
    {
        session()->remove('current_session_id');
        $this->currentSession = null;
        
        return [
            'success' => true,
            'message' => 'Session context cleared'
        ];
    }

    /**
     * Get session filter for views
     */
    public function getSessionFilter()
    {
        $currentSession = $this->getContextSession();
        $allSessions = $this->getActiveSessionsDropdown();
        
        return [
            'current_session' => $currentSession,
            'all_sessions' => $allSessions,
            'selected_session_id' => session()->get('current_session_id') ?: ($currentSession ? $currentSession['id'] : null)
        ];
    }

    /**
     * Ensure data belongs to current session
     */
    public function validateSessionOwnership($data, $sessionField = 'session_id')
    {
        $currentSessionId = $this->getCurrentSessionId();
        
        if (!$currentSessionId) {
            return true; // No session context, allow all
        }
        
        if (is_array($data)) {
            return isset($data[$sessionField]) && $data[$sessionField] == $currentSessionId;
        }
        
        if (is_object($data)) {
            return isset($data->$sessionField) && $data->$sessionField == $currentSessionId;
        }
        
        return false;
    }

    /**
     * Add session context to data
     */
    public function addSessionContext($data, $sessionField = 'session_id')
    {
        $sessionId = $this->getCurrentSessionId();
        
        if ($sessionId) {
            if (is_array($data)) {
                $data[$sessionField] = $sessionId;
            } elseif (is_object($data)) {
                $data->$sessionField = $sessionId;
            }
        }
        
        return $data;
    }
}
